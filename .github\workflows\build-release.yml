name: Build Release

on:
  push:
    tags:
      - "v*.*.*"

env:
  THEOS: ''
  GIT_TAG: ''
  XCODE_VERSION: '14.3.1'

jobs:
  build:
    name: Build and upload release package for TrollStore
    runs-on: macos-13

    steps:
      - name: Setup Xcode version
        uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: ${{ env.XCODE_VERSION }}

      - name: Install Homebrew dependencies
        run: |
          HOMEBREW_NO_AUTO_UPDATE=1 HOMEBREW_NO_INSTALLED_DEPENDENTS_CHECK=1 brew install dpkg make libplist openssl@3
          echo "/usr/local/opt/make/libexec/gnubin" >> $GITHUB_PATH

      - name: Checkout ldid
        uses: actions/checkout@v4
        with:
          repository: Lessica/ldid
          ref: master
          path: ldid

      - name: Build ldid
        run: |
          cd $GITHUB_WORKSPACE/ldid
          make install

      - name: Checkout XXTouchNG/theos
        uses: actions/checkout@v4
        with:
          repository: XXTouchNG/theos
          ref: 78ee784d8d3238982c9abdc58cd39919263648b1
          path: theos
          submodules: recursive

      - name: Add THEOS environment variables
        run: |
          rm -rf $GITHUB_WORKSPACE/theos/sdks
          echo "THEOS=$GITHUB_WORKSPACE/theos" >> $GITHUB_ENV

      - name: Checkout theos/sdks
        uses: actions/checkout@v4
        with:
          repository: theos/sdks
          ref: master
          path: ${{ env.THEOS }}/sdks

      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: Lessica/TrollSpeed
          ref: main
          path: TrollSpeed
          submodules: recursive

      - name: Setup build environment
        run: |
          echo "Available SDKs: $(find $THEOS/sdks -name "*.sdk" -maxdepth 1 -print)"
          echo "FINALPACKAGE=1" >> $GITHUB_ENV
          cd $GITHUB_WORKSPACE/TrollSpeed
          git fetch --tags
          echo "GIT_TAG=$(git describe --tags --always --abbrev=0)" >> $GITHUB_ENV

      - name: Build package
        run: |
          cd $GITHUB_WORKSPACE/TrollSpeed
          ./gen-control.sh ${{ env.GIT_TAG }}
          ./build.sh ${{ env.GIT_TAG }}
          THEOS_PACKAGE_SCHEME=rootless FINALPACKAGE=1 make package
          FINALPACKAGE=1 make clean
          FINALPACKAGE=1 make package

      - name: Collect symbols
        run: |
          cd $GITHUB_WORKSPACE/TrollSpeed
          find .theos/obj -name "*.dSYM" -print | zip -r packages/TrollSpeed_${{ env.GIT_TAG }}_symbols.zip -@

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: TrollSpeed_${{ env.GIT_TAG }}
          path: |
            TrollSpeed/packages/TrollSpeed_*.tipa
            TrollSpeed/packages/TrollSpeed+AppIntents16_*.tipa
            TrollSpeed/packages/ch.xxtou.hudapp_*.deb

      - name: Upload symbols
        uses: actions/upload-artifact@v4
        with:
          name: TrollSpeed_${{ env.GIT_TAG }}_symbols
          path: |
            TrollSpeed/packages/TrollSpeed_${{ env.GIT_TAG }}_symbols.zip

      - name: Upload release
        uses: softprops/action-gh-release@v1
        with:
          token: ${{ secrets.RELEASE_GITHUB_TOKEN }}
          body_path: TrollSpeed/CHANGELOG.md
          files: |
            TrollSpeed/packages/TrollSpeed_*.tipa
            TrollSpeed/packages/TrollSpeed+AppIntents16_*.tipa
            TrollSpeed/packages/ch.xxtou.hudapp_*.deb

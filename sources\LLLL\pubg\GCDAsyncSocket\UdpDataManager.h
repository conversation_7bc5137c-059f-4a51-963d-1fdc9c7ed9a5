
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

@interface UdpDataManager : NSObject

//@property(nonatomic,strong)UIWindow *DrawWindow;
@property(nonatomic,strong) UIViewController *DrawViewController;
@property(nonatomic,strong) UITextField *ZhiBoMetalView;

@property(nonatomic,assign) bool huizhi;
@property(nonatomic,assign) bool shexian;
@property(nonatomic,assign) bool LV3wuzi;
@property(nonatomic,assign) bool vehicle;
@property(nonatomic,assign) bool Airdrop;
@property(nonatomic,assign) bool wuhou;
@property(nonatomic,assign) bool handgun;
@property(nonatomic,assign) bool aimline;
@property(nonatomic,assign) bool aimquantype;
@property(nonatomic,assign) bool bname;
@property(nonatomic,assign) bool guge;
@property(nonatomic,assign) bool bshijiao;

@property(nonatomic,assign) int zhimiaoquan;
@property(nonatomic,assign) int zhimiaodis;
@property(nonatomic,assign) int aimpoint;
@property(nonatomic,assign) float zhimiaosudu;
@property(nonatomic,assign) float yupandaxiao;
@property(nonatomic,assign) float yaqiang;

@property(nonatomic,assign) float touchx;
@property(nonatomic,assign) float touchy;

@property(nonatomic,assign) float Radius;

+(UdpDataManager *)Share;
- (void)UdpInit:(UIViewController*) faUIViewController;
- (void)guozhibo:(bool)swtich;
@end

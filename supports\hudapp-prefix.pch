#if __has_include(<AvailabilityVersions.h>)
    #import <AvailabilityVersions.h>
#endif

#ifdef __OBJC__
    #if __has_include(<TargetConditionals.h>)
        #import <TargetConditionals.h>
    #endif

    #import <Foundation/Foundation.h>
    #import <UIKit/UIKit.h>
    #import <os/log.h>
    #import "rootless.h"
    #import "NSUserDefaults+Private.h"
#endif

#if DEBUG
    #define log_debug os_log_debug
    #define log_info os_log_info
    #define log_error os_log_error
#else
    #define log_debug(...)
    #define log_info(...)
    #define log_error(...)
#endif

#define FADE_OUT_DURATION 0.25

#if !NO_TROLL
    #define USER_DEFAULTS_PATH @"/var/mobile/Library/Preferences/com.LLLL.plugin.plist"
#else
    #define USER_DEFAULTS_PATH ([[NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES) firstObject] stringByAppendingPathComponent:@"com.LLLL.plugin.plist"])
#endif

// HUD -> APP: Notify APP that the HUD's view is appeared.
#define NOTIFY_LAUNCHED_HUD "ch.xxtou.notification.hud.launched"

// APP -> HUD: Notify HUD to dismiss itself.
#define NOTIFY_DISMISSAL_HUD "ch.xxtou.notification.hud.dismissal"

// APP -> HUD: Notify HUD that the user defaults has been changed by APP.
#define NOTIFY_RELOAD_HUD "ch.xxtou.notification.hud.reload"

// HUD -> APP: Notify APP that the user defaults has been changed by HUD.
#define NOTIFY_RELOAD_APP "ch.xxtou.notification.app.reload"

#ifdef __OBJC__
#import "hudapp-bridging-header.h"
#endif


#import "UdpDataManager.h"
#import <UIKit/UIKit.h>
#import "DrawView.h"
#import "HeeeNoScreenShotView.h"
#import "Weapon.h"
#import "FPSDisplay.h"
#import "ReadData.h"
#import <mach-o/dyld.h>
#import <AVFoundation/AVFoundation.h>
#import <MediaPlayer/MediaPlayer.h>
#import <dlfcn.h>
#import <objc/runtime.h>

#import <mach/vm_region.h>
#import <mach-o/dyld.h>
#import <mach/mach.h>
#import <dlfcn.h>
#include <pthread.h>

long dataaddr;
@interface UdpDataManager()
{
    AVAudioSession *audio;
    DrawView *draw;
    
    HeeeNoScreenShotView *noScreenShotView;
    HeeeNoScreenShotView *noScreenShotMenuView;
    
    NSString *SerialNumber;
    NSString *EndTimeString;
}

@end
 
@implementation UdpDataManager

+(UdpDataManager *)Share
{
    static UdpDataManager *manager=nil;
    static dispatch_once_t once;
    dispatch_once(&once, ^{
        manager=[[UdpDataManager alloc]init];
    });
    return manager;
}
- (void)UdpInit:(UIViewController*) faUIViewController {
    _DrawViewController = faUIViewController;
    
    [self varinit];
    
    [[Weapon SharedFuckData]initimage];
    
    [self drawinit];
    
    [self fpsdisplayinit];
}
-(void)varinit
{
    _zhimiaosudu = 0.2;
    _zhimiaoquan = 100;
    _yupandaxiao = 1.0;
    _zhimiaodis = 100;
    _aimpoint = 6;
    _wuhou = false;
    _handgun = false;
    _aimline = false;
    _aimquantype = false;
    _bname = false;
    _bshijiao = false;
    _yaqiang = 0.4;
    _Radius = 0;
    
    // 默认开启功能
    _guge = false;
    _shexian = false;
    _aimline = false;
    _handgun = true;
    _zhimiaoquan = 0; // 关闭自瞄圈
}
-(void)fpsdisplayinit
{
    [FPSDisplay shareFPSDisplay];
}
-(void)drawinit
{
//    noScreenShotView = [[HeeeNoScreenShotView alloc] initWithFrame:CGRectMake(0,0,0,0)];
//    [noScreenShotView setUserInteractionEnabled:NO];
//    [_DrawViewController.view addSubview:noScreenShotView];
    
    draw = [[DrawView alloc]init];
    draw.userInteractionEnabled = false;
    [_DrawViewController.view addSubview:draw];
    
    //==============初始化过直播视图===============
    _ZhiBoMetalView = [[UITextField alloc]initWithFrame:draw.frame];
    _ZhiBoMetalView.backgroundColor = [UIColor clearColor];
    _ZhiBoMetalView.secureTextEntry = YES;
    _ZhiBoMetalView.userInteractionEnabled = YES;
    [_ZhiBoMetalView.subviews.firstObject addSubview:draw];//添加Metal视图
    [_DrawViewController.view addSubview:_ZhiBoMetalView];
}

-(void)guozhibo:(bool)swtich
{
    _ZhiBoMetalView.secureTextEntry = swtich;
//    [noScreenShotView setsecure:swtich];
//    [noScreenShotMenuView setsecure:swtich];
}

@end

---
archs:           [ armv7, armv7s, arm64, arm64e, i386, x86_64 ]
platform:        ios
install-name:    /Library/Frameworks/CydiaSubstrate.framework/CydiaSubstrate
current-version: 0.0.0
compatibility-version: 0.0.0
exports:
  - archs:            [ armv7, armv7s, arm64, arm64e, i386, x86_64 ]
    symbols:          [ _MSCloseImage, _MSDebug, _MSFindAddress, _MSFindSymbol,
                        _MSGetImageByName, _MSHookClassPair, _MSHookFunction,
                        _MS<PERSON>ookMemory, _MS<PERSON>ookMessageEx, _MSImageAddress,
                        _MSMapImage ]
...

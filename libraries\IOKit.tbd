--- !tapi-tbd
tbd-version:     4
targets:         [ armv7-ios, armv7s-ios, arm64-ios, arm64e-ios ]
uuids:
  - target:          armv7-ios
    value:           B9723266-7EEF-3004-814F-37DC27C11C6D
  - target:          armv7s-ios
    value:           0DC88380-6968-362B-8B97-7059CE27B9A4
  - target:          arm64-ios
    value:           B67778BF-01FB-3B52-AB29-81C5244E6F9C
  - target:          arm64e-ios
    value:           DB237F62-AA09-34C3-99EE-47AFAACF8695
install-name:    '/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit'
current-version: 275
exports:
  - targets:         [ armv7-ios, armv7s-ios ]
    symbols:         [ _IOAVAudioFormatTypeString, _IOAVAudioSampleRate, _IOAVAudioSampleSize, 
                       _IOAVClassMatching, _IOAVDeviceGetPower, _IOAVDeviceGetProtectionType, 
                       _IOAVDeviceSetPower, _IOAVDeviceStartLink, _IOAVDeviceStopLink, 
                       _IOAVGetSupportedVideoTimingDataList, _IOAVGetTransportSupportsCEA, 
                       _IOAVGetTransportSupportsYCbCr, _IOAVGetVideoTimingITSource, 
                       _IOAVProtectionTypeString, _IOAVServiceGetProtectionType, 
                       _IOAVServiceSetPower, _IOAVServiceSupportsLink, _IOAVSupportedVideoTimingDataListTypeString, 
                       _IOAVVideoTimingCalculatedTypeString, _IOConnectMethodScalarIScalarO, 
                       _IOConnectMethodScalarIStructureI, _IOConnectMethodScalarIStructureO, 
                       _IOConnectMethodStructureIStructureO, _IODPControllerSetDownspreadModulationFrequency, 
                       _IODPControllerSetSupportsALPM, _IODPControllerSetSupportsScrambling, 
                       _IODPDeviceGetSinkCount, _IODPDeviceSetALPMEnabled, _IODPLinkMaxPixelRate, 
                       _IODPServiceSetLinkCheckEnable, _IODPVideoMinLinkRate, _IOHIDEventSystemConnectionGetTask, 
                       _IOHIDServiceConnectionCacheGetUnresponsiveness, _IOHIDServiceConnectionCacheSetUnresponsiveness, 
                       _IOMapMemory, _IOPMUserDidChangeCallback, _IOPSCopyChargeLog, 
                       __IOHIDCopyServiceIDs, __IOHIDEventSystemConnectionContainsEntitlement, 
                       __IOHIDEventSystemConnectionGetExecutablePath, __IOHIDEventSystemConnectionSetEventFilterMask, 
                       __IOHIDHasEntitlement, __IOHIDServiceCopyDebugDescriptionForClient, 
                       __IOHIDServiceCopyRecordForClient, ___CachedPropertiesRefreshApplierFunction, 
                       ___IOHIDElementRegister, ___IOHIDSession, ___IOHIDSessionQueueDidExecute, 
                       ___IOHIDSessionQueueWillExecute, ___IOHIDSessionRef, ___IOHIDSessionReportActivity, 
                       _gIOHIDLogLevel, _io_async_method_scalarI_scalarO, _io_async_method_scalarI_structureI, 
                       _io_async_method_scalarI_structureO, _io_async_method_structureI_structureO, 
                       _io_catalog_get_data, _io_catalog_get_gen_count, _io_catalog_module_loaded, 
                       _io_catalog_reset, _io_catalog_send_data, _io_catalog_terminate, 
                       _io_connect_add_client, _io_connect_async_method, _io_connect_get_notification_semaphore, 
                       _io_connect_get_service, _io_connect_map_memory, _io_connect_map_memory_into_task, 
                       _io_connect_method, _io_connect_method_scalarI_scalarO, _io_connect_method_scalarI_structureI, 
                       _io_connect_method_scalarI_structureO, _io_connect_method_structureI_structureO, 
                       _io_connect_method_var_output, _io_connect_set_notification_port, 
                       _io_connect_set_properties, _io_connect_unmap_memory, _io_connect_unmap_memory_from_task, 
                       _io_iterator_is_valid, _io_iterator_next, _io_iterator_reset, 
                       _io_object_conforms_to, _io_object_get_bundle_identifier, 
                       _io_object_get_class, _io_object_get_retain_count, _io_object_get_superclass, 
                       _io_pm_connection_copy_status, _io_ps_copy_chargelog, _io_registry_create_iterator, 
                       _io_registry_entry_create_iterator, _io_registry_entry_from_path, 
                       _io_registry_entry_from_path_ool, _io_registry_entry_get_child_iterator, 
                       _io_registry_entry_get_location_in_plane, _io_registry_entry_get_name, 
                       _io_registry_entry_get_name_in_plane, _io_registry_entry_get_parent_iterator, 
                       _io_registry_entry_get_path, _io_registry_entry_get_path_ool, 
                       _io_registry_entry_get_properties, _io_registry_entry_get_properties_bin, 
                       _io_registry_entry_get_property, _io_registry_entry_get_property_bin, 
                       _io_registry_entry_get_property_bytes, _io_registry_entry_get_property_recursively, 
                       _io_registry_entry_get_registry_entry_id, _io_registry_entry_in_plane, 
                       _io_registry_entry_set_properties, _io_registry_get_root_entry, 
                       _io_registry_iterator_enter_entry, _io_registry_iterator_exit_entry, 
                       _io_server_version, _io_service_acknowledge_notification, 
                       _io_service_add_interest_notification, _io_service_add_notification, 
                       _io_service_add_notification_bin, _io_service_add_notification_ool, 
                       _io_service_close, _io_service_get_authorization_id, _io_service_get_busy_state, 
                       _io_service_get_matching_service, _io_service_get_matching_service_bin, 
                       _io_service_get_matching_service_ool, _io_service_get_matching_services, 
                       _io_service_get_matching_services_bin, _io_service_get_matching_services_ool, 
                       _io_service_get_state, _io_service_match_property_table, _io_service_match_property_table_bin, 
                       _io_service_match_property_table_ool, _io_service_open_extended, 
                       _io_service_request_probe, _io_service_set_authorization_id, 
                       _io_service_wait_quiet, _kIOHIDServiceEnumerationRootQueue, 
                       _kIOHIDServiceInterruptRootQueue ]
  - targets:         [ arm64-ios, arm64e-ios ]
    symbols:         [ __ZN9DisplayID8checksumEPKvm ]
  - targets:         [ arm64-ios, arm64e-ios, armv7-ios, armv7s-ios ]
    symbols:         [ _IOAVAudioFormatString, _IOAVAudioGetChannelAllocation, _IOAVAudioGetChannelAllocationDefault, 
                       _IOAVAudioGetChannelLayoutData, _IOAVAudioGetSpeakerAllocationMask, 
                       _IOAVAudioInterfaceCopyChannelLayoutElements, _IOAVAudioInterfaceCopyDiagnosticsString, 
                       _IOAVAudioInterfaceCopyElements, _IOAVAudioInterfaceCopyProperties, 
                       _IOAVAudioInterfaceCopyProperty, _IOAVAudioInterfaceCreate, 
                       _IOAVAudioInterfaceCreateWithService, _IOAVAudioInterfaceGetLinkData, 
                       _IOAVAudioInterfaceGetLinkDataWithSource, _IOAVAudioInterfaceGetLocation, 
                       _IOAVAudioInterfaceGetService, _IOAVAudioInterfaceGetTypeID, 
                       _IOAVAudioInterfaceSetLogLevel, _IOAVAudioInterfaceSetLogLevelMask, 
                       _IOAVAudioInterfaceSetProperty, _IOAVAudioInterfaceStartLink, 
                       _IOAVAudioInterfaceStartLinkWithSource, _IOAVAudioInterfaceStopLink, 
                       _IOAVAudioInterfaceStopLinkWithSource, _IOAVAudioLinkGetBitRate, 
                       _IOAVAudioLinkGetHDMIAudioPacketType, _IOAVAudioLinkGetMaxStreamChannelCount, 
                       _IOAVAudioLinkGetMaxStreamSampleRate, _IOAVAudioLinkIsIEC61937, 
                       _IOAVAudioLinkSampleRateForFormat, _IOAVAudioSampleRateEnum, 
                       _IOAVAudioSampleRateScalar, _IOAVAudioSampleSizeEnum, _IOAVAudioSampleSizeScalar, 
                       _IOAVAudioSpeakerString, _IOAVCommandString, _IOAVConnectCallCopyMethod, 
                       _IOAVConnectCallSetMethod, _IOAVContentProtectionProtocolString, 
                       _IOAVContentProtectionTypeString, _IOAVControlInterfaceCopyDiagnosticsString, 
                       _IOAVControlInterfaceCopyProperties, _IOAVControlInterfaceCopyProperty, 
                       _IOAVControlInterfaceCreate, _IOAVControlInterfaceCreateWithService, 
                       _IOAVControlInterfaceGetLocation, _IOAVControlInterfaceGetService, 
                       _IOAVControlInterfaceGetTypeID, _IOAVControlInterfaceSetLogLevel, 
                       _IOAVControlInterfaceSetLogLevelMask, _IOAVControlInterfaceSetProperty, 
                       _IOAVControllerClearEventLog, _IOAVControllerCopyDiagnosticsString, 
                       _IOAVControllerCopyProperties, _IOAVControllerCopyProperty, 
                       _IOAVControllerCreate, _IOAVControllerCreateWithLocation, 
                       _IOAVControllerCreateWithService, _IOAVControllerForceHotPlugDetect, 
                       _IOAVControllerGetLocation, _IOAVControllerGetPower, _IOAVControllerGetTypeID, 
                       _IOAVControllerSetEventLogCommandMask, _IOAVControllerSetEventLogEventMask, 
                       _IOAVControllerSetEventLogSize, _IOAVControllerSetLogLevel, 
                       _IOAVControllerSetLogLevelMask, _IOAVControllerSetPower, _IOAVControllerSetProperty, 
                       _IOAVControllerSetProtectionType, _IOAVControllerSetVirtualDeviceMode, 
                       _IOAVControllerSleepDisplay, _IOAVControllerWakeDisplay, _IOAVCreateDiagnosticsReference, 
                       _IOAVCreateDiagnosticsReferenceWithLocation, _IOAVCreateDiagnosticsString, 
                       _IOAVCreateDiagnosticsStringWithLocation, _IOAVCreateStringWithAudioChannelLayoutData, 
                       _IOAVCreateStringWithAudioLinkData, _IOAVCreateStringWithData, 
                       _IOAVCreateStringWithElement, _IOAVCreateStringWithElements, 
                       _IOAVCreateStringWithVideoColorData, _IOAVCreateStringWithVideoLinkData, 
                       _IOAVCreateStringWithVideoTimingData, _IOAVDSCCapabilitiesGetMaxSlicesPerLine, 
                       _IOAVDSCCapabilitiesGetPeakPixelRateForMode, _IOAVDSCModeForPixelEncoding, 
                       _IOAVDSCSlicesPerLineScalar, _IOAVDeviceClearEventLog, _IOAVDeviceCopyDiagnosticsString, 
                       _IOAVDeviceCopyProperties, _IOAVDeviceCopyProperty, _IOAVDeviceCreate, 
                       _IOAVDeviceCreateWithLocation, _IOAVDeviceCreateWithService, 
                       _IOAVDeviceGetController, _IOAVDeviceGetLinkData, _IOAVDeviceGetLocation, 
                       _IOAVDeviceGetProtectionStatus, _IOAVDeviceGetTypeID, _IOAVDeviceReadI2C, 
                       _IOAVDeviceSetEventLogCommandMask, _IOAVDeviceSetEventLogEventMask, 
                       _IOAVDeviceSetEventLogSize, _IOAVDeviceSetLogLevel, _IOAVDeviceSetLogLevelMask, 
                       _IOAVDeviceSetProperty, _IOAVDeviceWriteI2C, _IOAVDisplayMemoryCreateWithName, 
                       _IOAVDisplayMemoryCreateWithService, _IOAVDisplayMemoryGetTypeID, 
                       _IOAVDisplayMemoryRead, _IOAVDisplayMemoryWrite, _IOAVEDIDIsStandard, 
                       _IOAVElementTypeString, _IOAVEventLogEventTypeString, _IOAVGetCEAVideoShortID, 
                       _IOAVGetCEAVideoShortIDWithData, _IOAVGetCEAVideoShortIDWithDataActive, 
                       _IOAVGetCEAVideoTimingData, _IOAVGetCEAVideoTimingDataWithShortID, 
                       _IOAVGetCVTVideoTimingData, _IOAVGetDMTVideoTimingData, _IOAVGetGTFVideoTimingData, 
                       _IOAVGetSPDInfoFrame, _IOAVGetVideoTimingData, _IOAVGetVideoTimingDataByID, 
                       _IOAVGetVideoTimingTable, _IOAVHDMIAudioClockRegenerationDataForLink, 
                       _IOAVHDMICharacterRate, _IOAVHDMIClockRate, _IOAVInfoFrameGetChecksum, 
                       _IOAVInfoFrameTypeString, _IOAVLinkSourceString, _IOAVLinkTypeString, 
                       _IOAVLocationString, _IOAVObjectConformsTo, _IOAVPropertyListCreateWithCFProperties, 
                       _IOAVProtectionStatusString, _IOAVRecoverableError, _IOAVServiceClearEventLog, 
                       _IOAVServiceCopyDiagnosticsString, _IOAVServiceCopyEDID, _IOAVServiceCopyPhysicalAddress, 
                       _IOAVServiceCopyProperties, _IOAVServiceCopyProperty, _IOAVServiceCreate, 
                       _IOAVServiceCreateWithLocation, _IOAVServiceCreateWithService, 
                       _IOAVServiceGetChosenContentProtection, _IOAVServiceGetContentProtectionCapabilities, 
                       _IOAVServiceGetDevice, _IOAVServiceGetHDCPAuthenticatedContentType, 
                       _IOAVServiceGetLinkData, _IOAVServiceGetLinkDataWithSource, 
                       _IOAVServiceGetLocation, _IOAVServiceGetPower, _IOAVServiceGetProtectionStatus, 
                       _IOAVServiceGetTypeID, _IOAVServiceReadI2C, _IOAVServiceSetContentProtectionCapabilities, 
                       _IOAVServiceSetContentProtectionPolicyOptions, _IOAVServiceSetContentProtectionSupportEnabled, 
                       _IOAVServiceSetEventLogCommandMask, _IOAVServiceSetEventLogEventMask, 
                       _IOAVServiceSetEventLogSize, _IOAVServiceSetHDRStaticMetadata, 
                       _IOAVServiceSetLogLevel, _IOAVServiceSetLogLevelMask, _IOAVServiceSetProperty, 
                       _IOAVServiceSetVirtualEDIDMode, _IOAVServiceStartInfoFrame, 
                       _IOAVServiceStartInfoFrameWithSource, _IOAVServiceStartLink, 
                       _IOAVServiceStartLinkWithSource, _IOAVServiceStopInfoFrame, 
                       _IOAVServiceStopInfoFrameWithSource, _IOAVServiceStopLink, 
                       _IOAVServiceStopLinkWithSource, _IOAVServiceWriteI2C, _IOAVStandardTypeString, 
                       _IOAVTransportString, _IOAVTransportSupportsCEA, _IOAVTransportSupportsRGBOnly, 
                       _IOAVVideoActiveFormatAspectRatio, _IOAVVideoAspectRatioString, 
                       _IOAVVideoAxisString, _IOAVVideoColorBitDepth, _IOAVVideoColorBitDepthMinimumForEOTF, 
                       _IOAVVideoColorBitDepthScalar, _IOAVVideoColorBitsPerPixel, 
                       _IOAVVideoColorCoefficientString, _IOAVVideoColorDynamicRangeString, 
                       _IOAVVideoColorEOTFString, _IOAVVideoColorMinimumBitsPerPixelDSC, 
                       _IOAVVideoColorSpaceString, _IOAVVideoColorimetryIsValid, 
                       _IOAVVideoColorimetryString, _IOAVVideoGetPixelClockTolerance, 
                       _IOAVVideoInterfaceCopyColorElements, _IOAVVideoInterfaceCopyDiagnosticsString, 
                       _IOAVVideoInterfaceCopyDisplayAttributes, _IOAVVideoInterfaceCopyProperties, 
                       _IOAVVideoInterfaceCopyProperty, _IOAVVideoInterfaceCopyTimingElements, 
                       _IOAVVideoInterfaceCreate, _IOAVVideoInterfaceCreateWithLocation, 
                       _IOAVVideoInterfaceCreateWithService, _IOAVVideoInterfaceGetLinkData, 
                       _IOAVVideoInterfaceGetLinkDataWithSource, _IOAVVideoInterfaceGetLocation, 
                       _IOAVVideoInterfaceGetService, _IOAVVideoInterfaceGetTypeID, 
                       _IOAVVideoInterfaceSetBounds, _IOAVVideoInterfaceSetColorDitherRemoval, 
                       _IOAVVideoInterfaceSetLogLevel, _IOAVVideoInterfaceSetLogLevelMask, 
                       _IOAVVideoInterfaceSetProperty, _IOAVVideoInterfaceSetRotation, 
                       _IOAVVideoInterfaceSetScreenVirtualTemperature, _IOAVVideoInterfaceStartLink, 
                       _IOAVVideoInterfaceStartLinkWithModes, _IOAVVideoInterfaceStartLinkWithSource, 
                       _IOAVVideoInterfaceStopLink, _IOAVVideoInterfaceStopLinkWithSource, 
                       _IOAVVideoLinkIsDolbyVision, _IOAVVideoLinkModeString, _IOAVVideoLinkRequiresHDMIScrambling, 
                       _IOAVVideoPixelEncodingIsDolbyVision, _IOAVVideoPixelEncodingIsLLDolbyVision, 
                       _IOAVVideoPixelEncodingString, _IOAVVideoScanInformationString, 
                       _IOAVVideoTimingGetActivePixelClock, _IOAVVideoTimingGetBlankingStyle, 
                       _IOAVVideoTimingGetITSource, _IOAVVideoTimingGetPixelClock, 
                       _IOAVVideoTimingGetSyncRateRounded, _IOAVVideoTimingIsVideoOptimized, 
                       _IOAVVideoTimingStandardString, _IOAVVideoTimingTypeString, 
                       _IOAVVideoTimingVideoOptimizedDelta, _IOAllowPowerChange, 
                       _IOBSDNameMatching, _IOCFSerialize, _IOCFURLWriteDataAndPropertiesToResource, 
                       _IOCFUnserialize, _IOCFUnserializeBinary, _IOCFUnserializeWithSize, 
                       _IOCFUnserializeparse, _IOCancelPowerChange, _IOCatalogueGetData, 
                       _IOCatalogueModuleLoaded, _IOCatalogueReset, _IOCatalogueSendData, 
                       _IOCatalogueTerminate, _IOCatlogueGetGenCount, _IOCloseConnection, 
                       _IOCompatibiltyNumber, _IOConnectAddClient, _IOConnectAddRef, 
                       _IOConnectCallAsyncMethod, _IOConnectCallAsyncScalarMethod, 
                       _IOConnectCallAsyncStructMethod, _IOConnectCallMethod, _IOConnectCallScalarMethod, 
                       _IOConnectCallStructMethod, _IOConnectGetService, _IOConnectMapMemory, 
                       _IOConnectMapMemory64, _IOConnectRelease, _IOConnectSetCFProperties, 
                       _IOConnectSetCFProperty, _IOConnectSetNotificationPort, _IOConnectTrap0, 
                       _IOConnectTrap1, _IOConnectTrap2, _IOConnectTrap3, _IOConnectTrap4, 
                       _IOConnectTrap5, _IOConnectTrap6, _IOConnectUnmapMemory, _IOConnectUnmapMemory64, 
                       _IOCopySystemLoadAdvisoryDetailed, _IOCreatePlugInInterfaceForService, 
                       _IOCreateReceivePort, _IODPAudioCodingType, _IODPCalculateM, 
                       _IODPCommandString, _IODPCompareLinkTrainingData, _IODPConstrainDriveSettings, 
                       _IODPConstrainedDriveSettings, _IODPControllerCreate, _IODPControllerCreateWithLocation, 
                       _IODPControllerCreateWithService, _IODPControllerGetAVController, 
                       _IODPControllerGetMaxLaneCount, _IODPControllerGetMaxLinkRate, 
                       _IODPControllerGetMinLaneCount, _IODPControllerGetMinLinkRate, 
                       _IODPControllerGetTypeID, _IODPControllerSetDriveSettings, 
                       _IODPControllerSetLaneCount, _IODPControllerSetLinkRate, _IODPControllerSetMaxLaneCount, 
                       _IODPControllerSetMaxLinkRate, _IODPControllerSetMinLaneCount, 
                       _IODPControllerSetMinLinkRate, _IODPControllerSetQualityPattern, 
                       _IODPControllerSetScramblingInhibited, _IODPControllerSetSupportsDownspread, 
                       _IODPControllerSetSupportsEnhancedMode, _IODPCreateStringWithLinkTrainingData, 
                       _IODPDeviceCreate, _IODPDeviceCreateWithLocation, _IODPDeviceCreateWithService, 
                       _IODPDeviceGetAVDevice, _IODPDeviceGetController, _IODPDeviceGetLinkTrainingData, 
                       _IODPDeviceGetMaxLaneCount, _IODPDeviceGetMaxLinkRate, _IODPDeviceGetRevisionMajor, 
                       _IODPDeviceGetRevisionMinor, _IODPDeviceGetSupportsDownspread, 
                       _IODPDeviceGetSupportsEnhancedMode, _IODPDeviceGetSymbolErrorCount, 
                       _IODPDeviceGetTypeID, _IODPDeviceReadDPCD, _IODPDeviceSetUpdateMode, 
                       _IODPDeviceSetUpdated, _IODPDeviceTypeString, _IODPDeviceWriteDPCD, 
                       _IODPDriveSettingsAreValid, _IODPDriveSettingsEqual, _IODPEventLogEventTypeString, 
                       _IODPInfoFrameSDP, _IODPLinkBandwidth, _IODPLinkBitRateForLinkSymbolClock, 
                       _IODPLinkRateEnum, _IODPLinkRateIsStandard, _IODPLinkRateRequiredForVideoBandwidth, 
                       _IODPLinkRateScalar, _IODPLinkSymbolClockForLinkBitRate, _IODPLinkSymbolRate, 
                       _IODPQualityPatternName, _IODPServiceCreate, _IODPServiceCreateWithLocation, 
                       _IODPServiceCreateWithService, _IODPServiceGetAVService, _IODPServiceGetDevice, 
                       _IODPServiceGetSinkCount, _IODPServiceGetSymbolErrorCount, 
                       _IODPServiceGetTypeID, _IODPServiceRetrainLink, _IODPStreamClockHz, 
                       _IODPTrainingPatternLength, _IODPTrainingPatternName, _IODPUnifiedDriveSettings, 
                       _IODPVideoBandwidth, _IODPVideoLinkMainStreamAttributeData, 
                       _IODPVideoLinkVideoStreamConfigurationSDP, _IODataQueueAllocateNotificationPort, 
                       _IODataQueueDataAvailable, _IODataQueueDequeue, _IODataQueueEnqueue, 
                       _IODataQueuePeek, _IODataQueueSetNotificationPort, _IODataQueueWaitForAvailableData, 
                       _IODeregisterApp, _IODeregisterForRemoteSystemPower, _IODeregisterForSystemPower, 
                       _IODestroyPlugInInterface, _IODispatchCalloutFromCFMessage, 
                       _IODispatchCalloutFromMessage, _IOEthernetControllerCreate, 
                       _IOEthernetControllerGetBSDSocket, _IOEthernetControllerGetIONetworkInterfaceObject, 
                       _IOEthernetControllerGetTypeID, _IOEthernetControllerReadPacket, 
                       _IOEthernetControllerRegisterBSDAttachCallback, _IOEthernetControllerRegisterDisableCallback, 
                       _IOEthernetControllerRegisterEnableCallback, _IOEthernetControllerRegisterPacketAvailableCallback, 
                       _IOEthernetControllerScheduleWithRunLoop, _IOEthernetControllerSetDispatchQueue, 
                       _IOEthernetControllerSetLinkStatus, _IOEthernetControllerSetPowerSavings, 
                       _IOEthernetControllerUnscheduleFromRunLoop, _IOEthernetControllerWritePacket, 
                       _IOGetSystemLoadAdvisory, _IOHIDAnalyticsEventActivate, _IOHIDAnalyticsEventAddField, 
                       _IOHIDAnalyticsEventAddHistogramField, _IOHIDAnalyticsEventCancel, 
                       _IOHIDAnalyticsEventCreate, _IOHIDAnalyticsEventSetIntegerValueForField, 
                       _IOHIDAnalyticsHistogramEventCreate, _IOHIDAnalyticsHistogramEventSetIntegerValue, 
                       _IOHIDCheckAccess, _IOHIDConnectionFilterActivate, _IOHIDConnectionFilterCancel, 
                       _IOHIDConnectionFilterCopyProperty, _IOHIDConnectionFilterCreate, 
                       _IOHIDConnectionFilterFilterEvent, _IOHIDConnectionFilterGetTypeID, 
                       _IOHIDConnectionFilterSetCancelHandler, _IOHIDConnectionFilterSetDispatchQueue, 
                       _IOHIDConnectionFilterSetProperty, _IOHIDCopyCFTypeParameter, 
                       _IOHIDCopyHIDParameterFromEventSystem, _IOHIDCreateSharedMemory, 
                       _IOHIDDeviceActivate, _IOHIDDeviceCancel, _IOHIDDeviceClose, 
                       _IOHIDDeviceConformsTo, _IOHIDDeviceCopyDescription, _IOHIDDeviceCopyMatchingElements, 
                       _IOHIDDeviceCopyValueMultiple, _IOHIDDeviceCopyValueMultipleWithCallback, 
                       _IOHIDDeviceCreate, _IOHIDDeviceGetProperty, _IOHIDDeviceGetRegistryEntryID, 
                       _IOHIDDeviceGetReport, _IOHIDDeviceGetReportWithCallback, 
                       _IOHIDDeviceGetService, _IOHIDDeviceGetTypeID, _IOHIDDeviceGetValue, 
                       _IOHIDDeviceGetValueWithCallback, _IOHIDDeviceGetValueWithOptions, 
                       _IOHIDDeviceOpen, _IOHIDDeviceRegisterInputReportCallback, 
                       _IOHIDDeviceRegisterInputReportWithTimeStampCallback, _IOHIDDeviceRegisterInputValueCallback, 
                       _IOHIDDeviceRegisterRemovalCallback, _IOHIDDeviceScheduleWithRunLoop, 
                       _IOHIDDeviceSetCancelHandler, _IOHIDDeviceSetDispatchQueue, 
                       _IOHIDDeviceSetInputValueMatching, _IOHIDDeviceSetInputValueMatchingMultiple, 
                       _IOHIDDeviceSetProperty, _IOHIDDeviceSetReport, _IOHIDDeviceSetReportWithCallback, 
                       _IOHIDDeviceSetValue, _IOHIDDeviceSetValueMultiple, _IOHIDDeviceSetValueMultipleWithCallback, 
                       _IOHIDDeviceSetValueWithCallback, _IOHIDDeviceUnscheduleFromRunLoop, 
                       _IOHIDElementAttach, _IOHIDElementCopyAttached, _IOHIDElementCreateWithDictionary, 
                       _IOHIDElementDetach, _IOHIDElementGetChildren, _IOHIDElementGetCollectionType, 
                       _IOHIDElementGetCookie, _IOHIDElementGetDevice, _IOHIDElementGetDuplicateIndex, 
                       _IOHIDElementGetLogicalMax, _IOHIDElementGetLogicalMin, _IOHIDElementGetName, 
                       _IOHIDElementGetParent, _IOHIDElementGetPhysicalMax, _IOHIDElementGetPhysicalMin, 
                       _IOHIDElementGetProperty, _IOHIDElementGetReportCount, _IOHIDElementGetReportID, 
                       _IOHIDElementGetReportSize, _IOHIDElementGetType, _IOHIDElementGetTypeID, 
                       _IOHIDElementGetUnit, _IOHIDElementGetUnitExponent, _IOHIDElementGetUsage, 
                       _IOHIDElementGetUsagePage, _IOHIDElementHasNullState, _IOHIDElementHasPreferredState, 
                       _IOHIDElementIsArray, _IOHIDElementIsNonLinear, _IOHIDElementIsRelative, 
                       _IOHIDElementIsVirtual, _IOHIDElementIsWrapping, _IOHIDElementSetProperty, 
                       _IOHIDEventAppendEvent, _IOHIDEventConformsTo, _IOHIDEventConformsToWithOptions, 
                       _IOHIDEventCopyDescription, _IOHIDEventCreate, _IOHIDEventCreateAccelerometerEvent, 
                       _IOHIDEventCreateAccelerometerEventWithType, _IOHIDEventCreateAmbientLightSensorEvent, 
                       _IOHIDEventCreateAtmosphericPressureEvent, _IOHIDEventCreateBiometricEvent, 
                       _IOHIDEventCreateBoundaryScrollEvent, _IOHIDEventCreateBrightnessEvent, 
                       _IOHIDEventCreateButtonEvent, _IOHIDEventCreateButtonEventWithPressure, 
                       _IOHIDEventCreateCollectionEvent, _IOHIDEventCreateCompassEvent, 
                       _IOHIDEventCreateCompassEventWithType, _IOHIDEventCreateCopy, 
                       _IOHIDEventCreateData, _IOHIDEventCreateDeviceOrientationEventWithUsage, 
                       _IOHIDEventCreateDigitizerEvent, _IOHIDEventCreateDigitizerFingerEvent, 
                       _IOHIDEventCreateDigitizerFingerEventWithQuality, _IOHIDEventCreateDigitizerStylusEvent, 
                       _IOHIDEventCreateDigitizerStylusEventWithPolarOrientation, 
                       _IOHIDEventCreateDockSwipeEvent, _IOHIDEventCreateFluidTouchGestureEvent, 
                       _IOHIDEventCreateForceEvent, _IOHIDEventCreateGameControllerEvent, 
                       _IOHIDEventCreateGenericGestureEvent, _IOHIDEventCreateGyroEvent, 
                       _IOHIDEventCreateGyroEventWithType, _IOHIDEventCreateKeyboardEvent, 
                       _IOHIDEventCreateLEDEvent, _IOHIDEventCreateMotionActivtyEvent, 
                       _IOHIDEventCreateMotionGestureEvent, _IOHIDEventCreateMouseEvent, 
                       _IOHIDEventCreateNavigationSwipeEvent, _IOHIDEventCreateOrientationEvent, 
                       _IOHIDEventCreatePolarOrientationEvent, _IOHIDEventCreateProgressEvent, 
                       _IOHIDEventCreateProximtyEvent, _IOHIDEventCreateQuaternionOrientationEvent, 
                       _IOHIDEventCreateRelativePointerEvent, _IOHIDEventCreateRotationEvent, 
                       _IOHIDEventCreateScaleEvent, _IOHIDEventCreateScrollEvent, 
                       _IOHIDEventCreateSwipeEvent, _IOHIDEventCreateSymbolicHotKeyEvent, 
                       _IOHIDEventCreateTranslationEvent, _IOHIDEventCreateUnicodeEvent, 
                       _IOHIDEventCreateUnicodeEventWithQuality, _IOHIDEventCreateVelocityEvent, 
                       _IOHIDEventCreateVendorDefinedEvent, _IOHIDEventCreateWithBytes, 
                       _IOHIDEventCreateWithData, _IOHIDEventCreateZoomToggleEvent, 
                       _IOHIDEventGetAttributeData, _IOHIDEventGetAttributeDataLength, 
                       _IOHIDEventGetAttributeDataPtr, _IOHIDEventGetChildren, _IOHIDEventGetDataLength, 
                       _IOHIDEventGetDataValue, _IOHIDEventGetDataValueWithOptions, 
                       _IOHIDEventGetDoubleValue, _IOHIDEventGetDoubleValueWithOptions, 
                       _IOHIDEventGetEvent, _IOHIDEventGetEventFlags, _IOHIDEventGetEventWithOptions, 
                       _IOHIDEventGetFloatMultiple, _IOHIDEventGetFloatMultipleWithOptions, 
                       _IOHIDEventGetFloatValue, _IOHIDEventGetFloatValueWithOptions, 
                       _IOHIDEventGetIntegerMultiple, _IOHIDEventGetIntegerMultipleWithOptions, 
                       _IOHIDEventGetIntegerValue, _IOHIDEventGetIntegerValueWithOptions, 
                       _IOHIDEventGetLatency, _IOHIDEventGetParent, _IOHIDEventGetPhase, 
                       _IOHIDEventGetPolicy, _IOHIDEventGetPosition, _IOHIDEventGetPositionWithOptions, 
                       _IOHIDEventGetScrollMomentum, _IOHIDEventGetSenderID, _IOHIDEventGetTimeStamp, 
                       _IOHIDEventGetType, _IOHIDEventGetTypeID, _IOHIDEventGetTypeString, 
                       _IOHIDEventGetVendorDefinedData, _IOHIDEventIsAbsolute, _IOHIDEventIsRepeat, 
                       _IOHIDEventQueueCreate, _IOHIDEventQueueCreateWithVM, _IOHIDEventQueueDequeueCopy, 
                       _IOHIDEventQueueEnqueue, _IOHIDEventQueueGetMemoryHandle, 
                       _IOHIDEventQueueGetNotificationPort, _IOHIDEventQueueGetTypeID, 
                       _IOHIDEventQueueIsActive, _IOHIDEventQueueResume, _IOHIDEventQueueSetNotificationPort, 
                       _IOHIDEventQueueStart, _IOHIDEventQueueStop, _IOHIDEventQueueSuspend, 
                       _IOHIDEventReadBytes, _IOHIDEventRemoveEvent, _IOHIDEventServerCreate, 
                       _IOHIDEventServerGetTypeID, _IOHIDEventServerScheduleWithDispatchQueue, 
                       _IOHIDEventServerUnscheduleFromDispatchQueue, _IOHIDEventSetAttributeData, 
                       _IOHIDEventSetDoubleMultiple, _IOHIDEventSetDoubleMultipleWithOptions, 
                       _IOHIDEventSetDoubleValue, _IOHIDEventSetDoubleValueWithOptions, 
                       _IOHIDEventSetEventFlags, _IOHIDEventSetFloatMultiple, _IOHIDEventSetFloatMultipleWithOptions, 
                       _IOHIDEventSetFloatValue, _IOHIDEventSetFloatValueWithOptions, 
                       _IOHIDEventSetIntegerMultiple, _IOHIDEventSetIntegerMultipleWithOptions, 
                       _IOHIDEventSetIntegerValue, _IOHIDEventSetIntegerValueWithOptions, 
                       _IOHIDEventSetPhase, _IOHIDEventSetPosition, _IOHIDEventSetPositionWithOptions, 
                       _IOHIDEventSetRepeat, _IOHIDEventSetScrollMomentum, _IOHIDEventSetSenderID, 
                       _IOHIDEventSetTimeStamp, _IOHIDEventSystemClient, _IOHIDEventSystemClientActivate, 
                       _IOHIDEventSystemClientCancel, _IOHIDEventSystemClientCopyProperty, 
                       _IOHIDEventSystemClientCopyServiceForRegistryID, _IOHIDEventSystemClientCopyServices, 
                       _IOHIDEventSystemClientCreate, _IOHIDEventSystemClientCreateSimpleClient, 
                       _IOHIDEventSystemClientCreateWithType, _IOHIDEventSystemClientDispatchEvent, 
                       _IOHIDEventSystemClientGetTypeID, _IOHIDEventSystemClientGetTypeString, 
                       _IOHIDEventSystemClientRegisterDeviceMatchingBlock, _IOHIDEventSystemClientRegisterDeviceMatchingCallback, 
                       _IOHIDEventSystemClientRegisterEventBlock, _IOHIDEventSystemClientRegisterEventCallback, 
                       _IOHIDEventSystemClientRegisterEventFilterBlock, _IOHIDEventSystemClientRegisterEventFilterBlockWithPriority, 
                       _IOHIDEventSystemClientRegisterEventFilterCallback, _IOHIDEventSystemClientRegisterEventFilterCallbackWithPriority, 
                       _IOHIDEventSystemClientRegisterPropertyChangedCallback, _IOHIDEventSystemClientRegisterResetCallback, 
                       _IOHIDEventSystemClientRegistryIDConformsTo, _IOHIDEventSystemClientScheduleWithDispatchQueue, 
                       _IOHIDEventSystemClientScheduleWithRunLoop, _IOHIDEventSystemClientSetCancelHandler, 
                       _IOHIDEventSystemClientSetDispatchQueue, _IOHIDEventSystemClientSetMatching, 
                       _IOHIDEventSystemClientSetMatchingMultiple, _IOHIDEventSystemClientSetProperty, 
                       _IOHIDEventSystemClientUnregisterDeviceMatchingBlock, _IOHIDEventSystemClientUnregisterDeviceMatchingCallback, 
                       _IOHIDEventSystemClientUnregisterEventBlock, _IOHIDEventSystemClientUnregisterEventCallback, 
                       _IOHIDEventSystemClientUnregisterEventFilterBlock, _IOHIDEventSystemClientUnregisterEventFilterCallback, 
                       _IOHIDEventSystemClientUnregisterPropertyChangedCallback, 
                       _IOHIDEventSystemClientUnregisterResetCallback, _IOHIDEventSystemClientUnscheduleFromDispatchQueue, 
                       _IOHIDEventSystemClientUnscheduleWithRunLoop, _IOHIDEventSystemClose, 
                       _IOHIDEventSystemConnectionCopyDescription, _IOHIDEventSystemConnectionDispatchEvent, 
                       _IOHIDEventSystemConnectionGetAttribute, _IOHIDEventSystemConnectionGetEntitlements, 
                       _IOHIDEventSystemConnectionGetTaskNamePort, _IOHIDEventSystemConnectionGetType, 
                       _IOHIDEventSystemConnectionGetTypeID, _IOHIDEventSystemConnectionGetTypeString, 
                       _IOHIDEventSystemConnectionGetUUID, _IOHIDEventSystemCopyConnections, 
                       _IOHIDEventSystemCopyEvent, _IOHIDEventSystemCopyMatchingServices, 
                       _IOHIDEventSystemCopyService, _IOHIDEventSystemCopyServices, 
                       _IOHIDEventSystemCreate, _IOHIDEventSystemGetProperty, _IOHIDEventSystemGetTypeID, 
                       _IOHIDEventSystemOpen, _IOHIDEventSystemRegisterConnectionAdditionCallback, 
                       _IOHIDEventSystemRegisterConnectionRemovalCallback, _IOHIDEventSystemRegisterPropertyChangedNotification, 
                       _IOHIDEventSystemRegisterServicesCallback, _IOHIDEventSystemSetCallback, 
                       _IOHIDEventSystemSetProperty, _IOHIDEventSystemUnregisterConnectionAdditionCallback, 
                       _IOHIDEventSystemUnregisterConnectionRemovalCallback, _IOHIDEventSystemUnregisterPropertyChangedNotification, 
                       _IOHIDEventSystemUnregisterServicesCallback, _IOHIDEventTypeGetName, 
                       _IOHIDGetAccelerationWithKey, _IOHIDGetActivityState, _IOHIDGetButtonEventNum, 
                       _IOHIDGetModifierLockState, _IOHIDGetMouseAcceleration, _IOHIDGetMouseButtonMode, 
                       _IOHIDGetParameter, _IOHIDGetScrollAcceleration, _IOHIDGetStateForSelector, 
                       _IOHIDManagerActivate, _IOHIDManagerCancel, _IOHIDManagerClose, 
                       _IOHIDManagerCopyDevices, _IOHIDManagerCreate, _IOHIDManagerGetProperty, 
                       _IOHIDManagerGetTypeID, _IOHIDManagerOpen, _IOHIDManagerRegisterDeviceMatchingCallback, 
                       _IOHIDManagerRegisterDeviceRemovalCallback, _IOHIDManagerRegisterInputReportCallback, 
                       _IOHIDManagerRegisterInputReportWithTimeStampCallback, _IOHIDManagerRegisterInputValueCallback, 
                       _IOHIDManagerSaveToPropertyDomain, _IOHIDManagerScheduleWithRunLoop, 
                       _IOHIDManagerSetCancelHandler, _IOHIDManagerSetDeviceMatching, 
                       _IOHIDManagerSetDeviceMatchingMultiple, _IOHIDManagerSetDispatchQueue, 
                       _IOHIDManagerSetInputValueMatching, _IOHIDManagerSetInputValueMatchingMultiple, 
                       _IOHIDManagerSetProperty, _IOHIDManagerUnscheduleFromRunLoop, 
                       _IOHIDNotificationCreate, _IOHIDNotificationGetClientCallback, 
                       _IOHIDNotificationGetClientRefcon, _IOHIDNotificationGetClientTarget, 
                       _IOHIDNotificationGetOwnerCallback, _IOHIDNotificationGetOwnerRefcon, 
                       _IOHIDNotificationGetOwnerTarget, _IOHIDNotificationGetTypeID, 
                       _IOHIDNotificationInvalidate, _IOHIDNotificationSignalWithBlock, 
                       _IOHIDPostEvent, _IOHIDPreferencesCopy, _IOHIDPreferencesCopyDomain, 
                       _IOHIDPreferencesCopyMultiple, _IOHIDPreferencesSet, _IOHIDPreferencesSetDomain, 
                       _IOHIDPreferencesSetMultiple, _IOHIDPreferencesSynchronize, 
                       _IOHIDQueueActivate, _IOHIDQueueAddElement, _IOHIDQueueCancel, 
                       _IOHIDQueueContainsElement, _IOHIDQueueCopyNextValue, _IOHIDQueueCopyNextValueWithTimeout, 
                       _IOHIDQueueCreate, _IOHIDQueueGetDepth, _IOHIDQueueGetDevice, 
                       _IOHIDQueueGetTypeID, _IOHIDQueueRegisterValueAvailableCallback, 
                       _IOHIDQueueRemoveElement, _IOHIDQueueScheduleWithRunLoop, 
                       _IOHIDQueueSetCancelHandler, _IOHIDQueueSetDepth, _IOHIDQueueSetDispatchQueue, 
                       _IOHIDQueueStart, _IOHIDQueueStop, _IOHIDQueueUnscheduleFromRunLoop, 
                       _IOHIDRegisterVirtualDisplay, _IOHIDRequestAccess, _IOHIDServiceClientConformsTo, 
                       _IOHIDServiceClientCopyDescription, _IOHIDServiceClientCopyEvent, 
                       _IOHIDServiceClientCopyMatchingEvent, _IOHIDServiceClientCopyProperties, 
                       _IOHIDServiceClientCopyProperty, _IOHIDServiceClientFastPathCopyEvent, 
                       _IOHIDServiceClientFastPathCopyEventWithStatus, _IOHIDServiceClientFastPathCopyProperty, 
                       _IOHIDServiceClientFastPathInit, _IOHIDServiceClientFastPathInvalidate, 
                       _IOHIDServiceClientFastPathSetProperty, _IOHIDServiceClientGetRegistryID, 
                       _IOHIDServiceClientGetTypeID, _IOHIDServiceClientRegisterRemovalBlock, 
                       _IOHIDServiceClientRegisterRemovalCallback, _IOHIDServiceClientSetElementValue, 
                       _IOHIDServiceClientSetProperty, _IOHIDServiceConformsTo, _IOHIDServiceConnectionCacheContainsKey, 
                       _IOHIDServiceConnectionCacheCopyDebugInfo, _IOHIDServiceConnectionCacheCopyValueForKey, 
                       _IOHIDServiceConnectionCacheCreate, _IOHIDServiceConnectionCacheGetReportDeadline, 
                       _IOHIDServiceConnectionCacheGetTypeID, _IOHIDServiceConnectionCacheSetReportDeadline, 
                       _IOHIDServiceConnectionCacheSetValueForKey, _IOHIDServiceCopyDescription, 
                       _IOHIDServiceCopyEvent, _IOHIDServiceCopyEventForClient, _IOHIDServiceCopyMatchingEvent, 
                       _IOHIDServiceCopyProperty, _IOHIDServiceCreatePropertyChangedNotification, 
                       _IOHIDServiceCreateRemovalNotification, _IOHIDServiceFilterClientNotification, 
                       _IOHIDServiceFilterClose, _IOHIDServiceFilterCompare, _IOHIDServiceFilterCopyPropertyForClient, 
                       _IOHIDServiceFilterCreate, _IOHIDServiceFilterCreateWithClass, 
                       _IOHIDServiceFilterFilterCopyEvent, _IOHIDServiceFilterFilterCopyMatchingEvent, 
                       _IOHIDServiceFilterFilterEvent, _IOHIDServiceFilterGetMatchScore, 
                       _IOHIDServiceFilterGetStateMask, _IOHIDServiceFilterGetType, 
                       _IOHIDServiceFilterGetTypeID, _IOHIDServiceFilterMatch, _IOHIDServiceFilterOpen, 
                       _IOHIDServiceFilterSchedule, _IOHIDServiceFilterSetCancelHandler, 
                       _IOHIDServiceFilterSetEventCallback, _IOHIDServiceFilterSetOutputEvent, 
                       _IOHIDServiceFilterSetPropertyForClient, _IOHIDServiceFilterUnschedule, 
                       _IOHIDServiceGetProperty, _IOHIDServiceGetRegistryID, _IOHIDServiceGetService, 
                       _IOHIDServiceGetTypeID, _IOHIDServiceMatchPropertyTable, _IOHIDServiceSetElementValue, 
                       _IOHIDServiceSetOutputEvent, _IOHIDServiceSetProperty, _IOHIDSessionAddService, 
                       _IOHIDSessionClose, _IOHIDSessionCopyEvent, _IOHIDSessionCreate, 
                       _IOHIDSessionFilterClose, _IOHIDSessionFilterCopyEvent, _IOHIDSessionFilterCreate, 
                       _IOHIDSessionFilterCreateWithClass, _IOHIDSessionFilterFilterCopyEvent, 
                       _IOHIDSessionFilterFilterEvent, _IOHIDSessionFilterFilterEventToConnection, 
                       _IOHIDSessionFilterGetPropertyForClient, _IOHIDSessionFilterGetType, 
                       _IOHIDSessionFilterGetTypeID, _IOHIDSessionFilterOpen, _IOHIDSessionFilterRegisterService, 
                       _IOHIDSessionFilterScheduleWithDispatchQueue, _IOHIDSessionFilterSetPropertyForClient, 
                       _IOHIDSessionFilterUnregisterService, _IOHIDSessionFilterUnscheduleFromDispatchQueue, 
                       _IOHIDSessionGetFilters, _IOHIDSessionGetProperty, _IOHIDSessionGetTypeID, 
                       _IOHIDSessionOpen, _IOHIDSessionRemoveService, _IOHIDSessionSetProperty, 
                       _IOHIDSetAccelerationWithKey, _IOHIDSetCFTypeParameter, _IOHIDSetCursorBounds, 
                       _IOHIDSetCursorEnable, _IOHIDSetEventsEnable, _IOHIDSetFixedMouseLocation, 
                       _IOHIDSetFixedMouseLocationWithTimeStamp, _IOHIDSetHIDParameterToEventSystem, 
                       _IOHIDSetModifierLockState, _IOHIDSetMouseAcceleration, _IOHIDSetMouseButtonMode, 
                       _IOHIDSetMouseLocation, _IOHIDSetOnScreenCursorBounds, _IOHIDSetParameter, 
                       _IOHIDSetScrollAcceleration, _IOHIDSetStateForSelector, _IOHIDSetVirtualDisplayBounds, 
                       _IOHIDTransactionAddElement, _IOHIDTransactionClear, _IOHIDTransactionCommit, 
                       _IOHIDTransactionCommitWithCallback, _IOHIDTransactionContainsElement, 
                       _IOHIDTransactionCreate, _IOHIDTransactionGetDevice, _IOHIDTransactionGetDirection, 
                       _IOHIDTransactionGetTypeID, _IOHIDTransactionGetValue, _IOHIDTransactionRemoveElement, 
                       _IOHIDTransactionScheduleWithRunLoop, _IOHIDTransactionSetDirection, 
                       _IOHIDTransactionSetValue, _IOHIDTransactionUnscheduleFromRunLoop, 
                       _IOHIDUnregisterVirtualDisplay, _IOHIDUserDeviceActivate, 
                       _IOHIDUserDeviceCancel, _IOHIDUserDeviceCopyProperty, _IOHIDUserDeviceCopyService, 
                       _IOHIDUserDeviceCreate, _IOHIDUserDeviceCreateWithOptions, 
                       _IOHIDUserDeviceCreateWithProperties, _IOHIDUserDeviceGetTypeID, 
                       _IOHIDUserDeviceHandleReport, _IOHIDUserDeviceHandleReportAsync, 
                       _IOHIDUserDeviceHandleReportAsyncWithTimeStamp, _IOHIDUserDeviceHandleReportWithTimeStamp, 
                       _IOHIDUserDeviceRegisterGetReportBlock, _IOHIDUserDeviceRegisterGetReportCallback, 
                       _IOHIDUserDeviceRegisterGetReportWithReturnLengthCallback, 
                       _IOHIDUserDeviceRegisterSetReportBlock, _IOHIDUserDeviceRegisterSetReportCallback, 
                       _IOHIDUserDeviceScheduleWithDispatchQueue, _IOHIDUserDeviceScheduleWithRunLoop, 
                       _IOHIDUserDeviceSetCancelHandler, _IOHIDUserDeviceSetDispatchQueue, 
                       _IOHIDUserDeviceSetProperty, _IOHIDUserDeviceUnscheduleFromDispatchQueue, 
                       _IOHIDUserDeviceUnscheduleFromRunLoop, _IOHIDValueCreateWithBytes, 
                       _IOHIDValueCreateWithBytesNoCopy, _IOHIDValueCreateWithIntegerValue, 
                       _IOHIDValueGetBytePtr, _IOHIDValueGetElement, _IOHIDValueGetIntegerValue, 
                       _IOHIDValueGetLength, _IOHIDValueGetScaledValue, _IOHIDValueGetTimeStamp, 
                       _IOHIDValueGetTypeID, _IOHIDVirtualServiceClientCreate, _IOHIDVirtualServiceClientCreateWithCallbacks, 
                       _IOHIDVirtualServiceClientDispatchEvent, _IOHIDVirtualServiceClientRemove, 
                       _IOInitContainerClasses, _IOIteratorIsValid, _IOIteratorNext, 
                       _IOIteratorReset, _IOKitGetBusyState, _IOKitWaitQuiet, _IOMIGMachPortCacheAdd, 
                       _IOMIGMachPortCacheCopy, _IOMIGMachPortCacheRemove, _IOMIGMachPortCreate, 
                       _IOMIGMachPortGetPort, _IOMIGMachPortGetTypeID, _IOMIGMachPortRegisterDemuxCallback, 
                       _IOMIGMachPortRegisterTerminationCallback, _IOMIGMachPortScheduleWithDispatchQueue, 
                       _IOMIGMachPortScheduleWithRunLoop, _IOMIGMachPortUnscheduleFromDispatchQueue, 
                       _IOMIGMachPortUnscheduleFromRunLoop, _IOMasterPort, _IONetworkClose, 
                       _IONetworkGetDataCapacity, _IONetworkGetDataHandle, _IONetworkGetPacketFiltersMask, 
                       _IONetworkOpen, _IONetworkReadData, _IONetworkResetData, _IONetworkSetPacketFiltersMask, 
                       _IONetworkWriteData, _IONotificationPortCreate, _IONotificationPortDestroy, 
                       _IONotificationPortGetMachPort, _IONotificationPortGetRunLoopSource, 
                       _IONotificationPortSetDispatchQueue, _IONotificationPortSetImportanceReceiver, 
                       _IOObjectConformsTo, _IOObjectCopyBundleIdentifierForClass, 
                       _IOObjectCopyClass, _IOObjectCopySuperclassForClass, _IOObjectGetClass, 
                       _IOObjectGetKernelRetainCount, _IOObjectGetRetainCount, _IOObjectGetUserRetainCount, 
                       _IOObjectIsEqualTo, _IOObjectRelease, _IOObjectRetain, _IOOpenConnection, 
                       _IOOpenFirmwarePathMatching, _IOPMActivateSystemPowerSettings, 
                       _IOPMAllowRemotePowerChange, _IOPMAllowsBackgroundTask, _IOPMAllowsPushServiceTask, 
                       _IOPMAssertionCopyProperties, _IOPMAssertionCreate, _IOPMAssertionCreateWithAutoTimeout, 
                       _IOPMAssertionCreateWithDescription, _IOPMAssertionCreateWithName, 
                       _IOPMAssertionCreateWithProperties, _IOPMAssertionCreateWithResourceList, 
                       _IOPMAssertionDeclareNotificationEvent, _IOPMAssertionDeclareSystemActivity, 
                       _IOPMAssertionDeclareSystemActivityWithProperties, _IOPMAssertionDeclareUserActivity, 
                       _IOPMAssertionNotify, _IOPMAssertionRelease, _IOPMAssertionRetain, 
                       _IOPMAssertionSetBTCollection, _IOPMAssertionSetProcessState, 
                       _IOPMAssertionSetProperty, _IOPMAssertionSetTimeout, _IOPMCancelAllRepeatingPowerEvents, 
                       _IOPMCancelAllScheduledPowerEvents, _IOPMCancelScheduledPowerEvent, 
                       _IOPMChangeSystemActivityAssertionBehavior, _IOPMClaimSystemWakeEvent, 
                       _IOPMConnectionAcknowledgeEvent, _IOPMConnectionAcknowledgeEventWithOptions, 
                       _IOPMConnectionCreate, _IOPMConnectionGetSystemCapabilities, 
                       _IOPMConnectionRelease, _IOPMConnectionScheduleWithRunLoop, 
                       _IOPMConnectionSetDispatchQueue, _IOPMConnectionSetNotification, 
                       _IOPMConnectionUnscheduleFromRunLoop, _IOPMCopyActivePMPreferences, 
                       _IOPMCopyAssertionActivityAggregate, _IOPMCopyAssertionActivityAggregateWithAllocator, 
                       _IOPMCopyAssertionActivityLog, _IOPMCopyAssertionActivityLogWithAllocator, 
                       _IOPMCopyAssertionActivityUpdate, _IOPMCopyAssertionActivityUpdateWithAllocator, 
                       _IOPMCopyAssertionsByProcess, _IOPMCopyAssertionsByProcessWithAllocator, 
                       _IOPMCopyAssertionsByType, _IOPMCopyAssertionsStatus, _IOPMCopyBatteryHeatMap, 
                       _IOPMCopyBatteryInfo, _IOPMCopyCPUPowerStatus, _IOPMCopyConnectionStatus, 
                       _IOPMCopyCurrentScheduledWake, _IOPMCopyCycleCountData, _IOPMCopyDefaultPreferences, 
                       _IOPMCopyDeviceRestartPreventers, _IOPMCopyFromPrefs, _IOPMCopyHIDPostEventHistory, 
                       _IOPMCopyInactiveAssertionsByProcess, _IOPMCopyKioskModeData, 
                       _IOPMCopyPMPreferences, _IOPMCopyPowerHistory, _IOPMCopyPowerHistoryDetailed, 
                       _IOPMCopyPowerStateInfo, _IOPMCopyPreferencesOnFile, _IOPMCopyRepeatingPowerEvents, 
                       _IOPMCopyScheduledPowerEvents, _IOPMCopySleepPreventersList, 
                       _IOPMCopySleepPreventersListWithID, _IOPMCopySleepWakeFailure, 
                       _IOPMCopySystemPowerSettings, _IOPMCopyUPSShutdownLevels, 
                       _IOPMCopyUserActivityLevelDescription, _IOPMCtlAssertionType, 
                       _IOPMDeclareNetworkClientActivity, _IOPMDisableAsyncAssertions, 
                       _IOPMEnableAsyncAssertions, _IOPMFeatureIsAvailable, _IOPMFeatureIsAvailableWithSupportedTable, 
                       _IOPMFindPowerManagement, _IOPMGetActivePushConnectionState, 
                       _IOPMGetAggressiveness, _IOPMGetCapabilitiesDescription, _IOPMGetDarkWakeThermalEmergencyCount, 
                       _IOPMGetLastWakeTime, _IOPMGetPerformanceWarningLevel, _IOPMGetSleepServicesActive, 
                       _IOPMGetThermalWarningLevel, _IOPMGetUUID, _IOPMGetUserActivityLevel, 
                       _IOPMGetValueInt, _IOPMIsADarkWake, _IOPMIsASilentWake, _IOPMIsASleep, 
                       _IOPMIsAUserWake, _IOPMLogWakeProgress, _IOPMPerformBlockWithAssertion, 
                       _IOPMRegisterForRemoteSystemPower, _IOPMRegisterPrefsChangeNotification, 
                       _IOPMRemoveIrrelevantProperties, _IOPMRequestSysWake, _IOPMRevertPMPreferences, 
                       _IOPMScheduleAssertionExceptionNotification, _IOPMSchedulePowerEvent, 
                       _IOPMScheduleRepeatingPowerEvent, _IOPMScheduleUserActiveChangedNotification, 
                       _IOPMScheduleUserActivityLevelNotification, _IOPMScheduleUserActivityLevelNotificationWithTimeout, 
                       _IOPMSetActivePushConnectionState, _IOPMSetAggressiveness, 
                       _IOPMSetAssertionActivityAggregate, _IOPMSetAssertionActivityLog, 
                       _IOPMSetAssertionExceptionLimits, _IOPMSetBTWakeInterval, 
                       _IOPMSetDWLingerInterval, _IOPMSetDebugFlags, _IOPMSetDesktopMode, 
                       _IOPMSetPMPreference, _IOPMSetPMPreferences, _IOPMSetReservePowerMode, 
                       _IOPMSetSleepServicesWakeTimeCap, _IOPMSetSystemPowerSetting, 
                       _IOPMSetUPSShutdownLevels, _IOPMSetUserActivityIdleTimeout, 
                       _IOPMSetValueInt, _IOPMSkylightCheckIn, _IOPMSkylightCheckInWithCapability, 
                       _IOPMSleepEnabled, _IOPMSleepSystem, _IOPMSleepSystemWithOptions, 
                       _IOPMSleepWakeCopyUUID, _IOPMSleepWakeSetUUID, _IOPMUnregisterExceptionNotification, 
                       _IOPMUnregisterNotification, _IOPMUnregisterPrefsChangeNotification, 
                       _IOPMUserIsActive, _IOPMUsingDefaultPreferences, _IOPMWriteToPrefs, 
                       _IOPSAccCreateAttachNotification, _IOPSAccCreateLimitedPowerNotification, 
                       _IOPSAccNotificationCreateRunLoopSource, _IOPSCopyExternalPowerAdapterDetails, 
                       _IOPSCopyInternalBatteriesArray, _IOPSCopyPowerSourcesByType, 
                       _IOPSCopyPowerSourcesInfo, _IOPSCopyPowerSourcesList, _IOPSCopyUPSArray, 
                       _IOPSCreateLimitedPowerNotification, _IOPSCreatePowerSource, 
                       _IOPSDrawingUnlimitedPower, _IOPSGetActiveBattery, _IOPSGetActiveUPS, 
                       _IOPSGetBatteryWarningLevel, _IOPSGetPercentRemaining, _IOPSGetPowerSourceDescription, 
                       _IOPSGetProvidingPowerSourceType, _IOPSGetSupportedPowerSources, 
                       _IOPSGetTimeRemainingEstimate, _IOPSNotificationCreateRunLoopSource, 
                       _IOPSPowerSourceSupported, _IOPSReleasePowerSource, _IOPSRequestBatteryUpdate, 
                       _IOPSSetPowerSourceDetails, _IORegisterApp, _IORegisterClient, 
                       _IORegisterForSystemPower, _IORegistryCreateEnumerator, _IORegistryCreateIterator, 
                       _IORegistryDisposeEnumerator, _IORegistryEntryCopyFromPath, 
                       _IORegistryEntryCopyPath, _IORegistryEntryCreateCFProperties, 
                       _IORegistryEntryCreateCFProperty, _IORegistryEntryCreateIterator, 
                       _IORegistryEntryFromPath, _IORegistryEntryGetChildEntry, _IORegistryEntryGetChildIterator, 
                       _IORegistryEntryGetLocationInPlane, _IORegistryEntryGetName, 
                       _IORegistryEntryGetNameInPlane, _IORegistryEntryGetParentEntry, 
                       _IORegistryEntryGetParentIterator, _IORegistryEntryGetPath, 
                       _IORegistryEntryGetProperty, _IORegistryEntryGetRegistryEntryID, 
                       _IORegistryEntryIDMatching, _IORegistryEntryInPlane, _IORegistryEntrySearchCFProperty, 
                       _IORegistryEntrySetCFProperties, _IORegistryEntrySetCFProperty, 
                       _IORegistryEnumeratorNextConforming, _IORegistryEnumeratorReset, 
                       _IORegistryGetRootEntry, _IORegistryIteratorEnterEntry, _IORegistryIteratorExitEntry, 
                       _IOServiceAddInterestNotification, _IOServiceAddMatchingNotification, 
                       _IOServiceAddNotification, _IOServiceAuthorize, _IOServiceClose, 
                       _IOServiceGetBusyState, _IOServiceGetBusyStateAndTime, _IOServiceGetMatchingService, 
                       _IOServiceGetMatchingServices, _IOServiceGetState, _IOServiceMatchPropertyTable, 
                       _IOServiceMatching, _IOServiceNameMatching, _IOServiceOFPathToBSDName, 
                       _IOServiceOpen, _IOServiceOpenAsFileDescriptor, _IOServiceRequestProbe, 
                       _IOServiceWaitQuiet, _IOSetNotificationPort, _IOURLCreateDataAndPropertiesFromResource, 
                       _IOURLCreatePropertyFromResource, _IOUSBDevicDeviceDescriptionGetTypeID, 
                       _IOUSBDeviceControllerCreate, _IOUSBDeviceControllerCreateDefaultDescription, 
                       _IOUSBDeviceControllerCreateWithService, _IOUSBDeviceControllerForceOffBus, 
                       _IOUSBDeviceControllerGetService, _IOUSBDeviceControllerGetTypeID, 
                       _IOUSBDeviceControllerGoOffAndOnBus, _IOUSBDeviceControllerRegisterArrivalCallback, 
                       _IOUSBDeviceControllerRemoveArrivalCallback, _IOUSBDeviceControllerSendCommand, 
                       _IOUSBDeviceControllerSetDescription, _IOUSBDeviceControllerSetPreferredConfiguration, 
                       _IOUSBDeviceDataCreate, _IOUSBDeviceDataGetBytePtr, _IOUSBDeviceDataGetCapacity, 
                       _IOUSBDeviceDataGetMapToken, _IOUSBDeviceDataGetTypeID, _IOUSBDeviceDescriptionAppendConfiguration, 
                       _IOUSBDeviceDescriptionAppendConfigurationWithInterface, _IOUSBDeviceDescriptionAppendConfigurationWithInterfaces, 
                       _IOUSBDeviceDescriptionAppendConfigurationWithoutAttributes, 
                       _IOUSBDeviceDescriptionAppendInterfaceToConfiguration, _IOUSBDeviceDescriptionAppendInterfacesToConfiguration, 
                       _IOUSBDeviceDescriptionCopyInterfaces, _IOUSBDeviceDescriptionCreate, 
                       _IOUSBDeviceDescriptionCreateFromController, _IOUSBDeviceDescriptionCreateFromControllerWithType, 
                       _IOUSBDeviceDescriptionCreateFromDefaults, _IOUSBDeviceDescriptionCreateFromDefaultsAndController, 
                       _IOUSBDeviceDescriptionCreateWithConfigurationInterfaces, 
                       _IOUSBDeviceDescriptionCreateWithType, _IOUSBDeviceDescriptionGetAllowOverride, 
                       _IOUSBDeviceDescriptionGetClass, _IOUSBDeviceDescriptionGetManufacturerString, 
                       _IOUSBDeviceDescriptionGetMatchingConfiguration, _IOUSBDeviceDescriptionGetProductID, 
                       _IOUSBDeviceDescriptionGetProductString, _IOUSBDeviceDescriptionGetProtocol, 
                       _IOUSBDeviceDescriptionGetSerialString, _IOUSBDeviceDescriptionGetSubClass, 
                       _IOUSBDeviceDescriptionGetVendorID, _IOUSBDeviceDescriptionGetVersion, 
                       _IOUSBDeviceDescriptionRemoveAllConfigurations, _IOUSBDeviceDescriptionSetAllowOverride, 
                       _IOUSBDeviceDescriptionSetClass, _IOUSBDeviceDescriptionSetProductID, 
                       _IOUSBDeviceDescriptionSetProtocol, _IOUSBDeviceDescriptionSetSerialString, 
                       _IOUSBDeviceDescriptionSetSubClass, _IOUSBDeviceDescriptionSetUDIDString, 
                       _IOUSBDeviceDescriptionSetVendorID, _NXClickTime, _NXCloseEventStatus, 
                       _NXEventSystemInfo, _NXGetClickSpace, _NXGetKeyMapping, _NXKeyMappingLength, 
                       _NXKeyRepeatInterval, _NXKeyRepeatThreshold, _NXOpenEventStatus, 
                       _NXResetKeyboard, _NXResetMouse, _NXSetClickSpace, _NXSetClickTime, 
                       _NXSetKeyMapping, _NXSetKeyRepeatInterval, _NXSetKeyRepeatThreshold, 
                       _OSGetNotificationFromMessage, _OSKEXT_BUILD_DATE, _OSKextAuthenticate, 
                       _OSKextAuthenticateDependencies, _OSKextCopyAllDependencies, 
                       _OSKextCopyAllRequestedIdentifiers, _OSKextCopyArchitectures, 
                       _OSKextCopyContainerForPluginKext, _OSKextCopyDeclaredDependencies, 
                       _OSKextCopyDependents, _OSKextCopyDiagnostics, _OSKextCopyExecutableForArchitecture, 
                       _OSKextCopyExecutableName, _OSKextCopyIndirectDependencies, 
                       _OSKextCopyInfoDictionary, _OSKextCopyKextsWithIdentifier, 
                       _OSKextCopyKextsWithIdentifiers, _OSKextCopyLinkDependencies, 
                       _OSKextCopyLoadList, _OSKextCopyLoadListForKexts, _OSKextCopyLoadedKextInfo, 
                       _OSKextCopyLoadedKextInfoByUUID, _OSKextCopyPersonalitiesArray, 
                       _OSKextCopyPersonalitiesOfKexts, _OSKextCopyPlugins, _OSKextCopyResource, 
                       _OSKextCopySymbolReferences, _OSKextCopyUUIDForAddress, _OSKextCopyUUIDForArchitecture, 
                       _OSKextCreate, _OSKextCreateKextsFromMkextData, _OSKextCreateKextsFromMkextFile, 
                       _OSKextCreateKextsFromURL, _OSKextCreateKextsFromURLs, _OSKextCreateLoadedKextInfo, 
                       _OSKextCreateMkext, _OSKextCreateWithIdentifier, _OSKextDeclaresExecutable, 
                       _OSKextDeclaresUserExecutable, _OSKextDependenciesAreLoadableInSafeBoot, 
                       _OSKextDependsOnKext, _OSKextExecutableVariant, _OSKextFilterRequiredKexts, 
                       _OSKextFindLinkDependencies, _OSKextFlushDependencies, _OSKextFlushDiagnostics, 
                       _OSKextFlushInfoDictionary, _OSKextFlushLoadInfo, _OSKextGetActualSafeBoot, 
                       _OSKextGetAllKexts, _OSKextGetArchitecture, _OSKextGetCompatibleKextWithIdentifier, 
                       _OSKextGetCompatibleVersion, _OSKextGetExecutableURL, _OSKextGetIdentifier, 
                       _OSKextGetKernelExecutableURL, _OSKextGetKextWithIdentifier, 
                       _OSKextGetKextWithIdentifierAndVersion, _OSKextGetKextWithURL, 
                       _OSKextGetLoadAddress, _OSKextGetLoadTag, _OSKextGetLoadedKextWithIdentifier, 
                       _OSKextGetLogFilter, _OSKextGetRecordsDiagnostics, _OSKextGetRunningKernelArchitecture, 
                       _OSKextGetSimulatedSafeBoot, _OSKextGetSystemExtensionsFolderURLs, 
                       _OSKextGetTargetString, _OSKextGetTypeID, _OSKextGetURL, _OSKextGetUserExecutableURL, 
                       _OSKextGetUsesCaches, _OSKextGetValueForInfoDictionaryKey, 
                       _OSKextGetVersion, _OSKextHasLogOrDebugFlags, _OSKextIsAuthentic, 
                       _OSKextIsCompatibleWithVersion, _OSKextIsFromMkext, _OSKextIsInterface, 
                       _OSKextIsKernelComponent, _OSKextIsLibrary, _OSKextIsLoadable, 
                       _OSKextIsLoadableInSafeBoot, _OSKextIsLoaded, _OSKextIsLoggingEnabled, 
                       _OSKextIsPlugin, _OSKextIsStarted, _OSKextIsValid, _OSKextLoad, 
                       _OSKextLoadWithOptions, _OSKextLog, _OSKextLogCFString, _OSKextLogDependencyGraph, 
                       _OSKextLogDiagnostics, _OSKextMatchesRequiredFlags, _OSKextOtherVersionIsLoaded, 
                       _OSKextParseVersionCFString, _OSKextParseVersionString, _OSKextReadLoadedKextInfo, 
                       _OSKextRemoveKextPersonalitiesFromKernel, _OSKextRemovePersonalitiesForIdentifierFromKernel, 
                       _OSKextResolveDependencies, _OSKextSendKextPersonalitiesToKernel, 
                       _OSKextSendPersonalitiesOfKextsToKernel, _OSKextSendPersonalitiesToKernel, 
                       _OSKextSetArchitecture, _OSKextSetExecutableSuffix, _OSKextSetLoadAddress, 
                       _OSKextSetLogFilter, _OSKextSetLogOutputFunction, _OSKextSetLoggingEnabled, 
                       _OSKextSetRecordsDiagnostics, _OSKextSetSimulatedSafeBoot, 
                       _OSKextSetTargetString, _OSKextSetUsesCaches, _OSKextStart, 
                       _OSKextStop, _OSKextSupportsArchitecture, _OSKextUnload, _OSKextUnloadKextWithIdentifier, 
                       _OSKextVLog, _OSKextVLogCFString, _OSKextValidate, _OSKextValidateDependencies, 
                       _OSKextVersionGetString, __CFURLCopyAbsolutePath, __IOAVCreateStringOfColorIDs, 
                       __IOAVElementListGetElementIDAtIndex, __IOAVStringAppendIndendationAndFormat, 
                       __IODataQueueDequeue, __IODataQueueEnqueueWithReadCallback, 
                       __IODataQueuePeek, __IODispatchCalloutWithDispatch, __IOHIDArrayAppendSInt64, 
                       __IOHIDCFArrayApplyBlock, __IOHIDCFDictionaryApplyBlock, __IOHIDCFSetApplyBlock, 
                       __IOHIDCallbackApplier, __IOHIDCopyServiceClientInfo, __IOHIDCreateBinaryData, 
                       __IOHIDCreateTimeString, __IOHIDDebugEventAddPerfData, __IOHIDDebugTrace, 
                       __IOHIDDeviceCreatePrivate, __IOHIDDeviceGetIOCFPlugInInterface, 
                       __IOHIDDeviceReleasePrivate, __IOHIDDictionaryAddCStr, __IOHIDDictionaryAddSInt32, 
                       __IOHIDDictionaryAddSInt64, __IOHIDElementCreatePrivate, __IOHIDElementCreateWithElement, 
                       __IOHIDElementCreateWithParentAndData, __IOHIDElementGetCalibrationInfo, 
                       __IOHIDElementGetFlags, __IOHIDElementGetLength, __IOHIDElementGetValue, 
                       __IOHIDElementReleasePrivate, __IOHIDElementSetDevice, __IOHIDElementSetDeviceInterface, 
                       __IOHIDElementSetValue, __IOHIDEventCopyAttachment, __IOHIDEventCreate, 
                       __IOHIDEventEqual, __IOHIDEventGetContext, __IOHIDEventQueueSerializeState, 
                       __IOHIDEventRemoveAttachment, __IOHIDEventSetAttachment, __IOHIDEventSetContext, 
                       __IOHIDEventSystemAddConnection, __IOHIDEventSystemAddService, 
                       __IOHIDEventSystemAddServiceForConnection, __IOHIDEventSystemClientCopyEventForService, 
                       __IOHIDEventSystemClientCopyMatchingEventForService, __IOHIDEventSystemClientCopyPropertiesForService, 
                       __IOHIDEventSystemClientCopyPropertyForService, __IOHIDEventSystemClientDispatchEventFilter, 
                       __IOHIDEventSystemClientDispatchPropertiesChanged, __IOHIDEventSystemClientRegisterClientRecordsChangedBlock, 
                       __IOHIDEventSystemClientRegisterClientRecordsChangedCallback, 
                       __IOHIDEventSystemClientRegisterServiceRecordsChangedBlock, 
                       __IOHIDEventSystemClientRegisterServiceRecordsChangedCallback, 
                       __IOHIDEventSystemClientServiceConformsTo, __IOHIDEventSystemClientSetElementValueForService, 
                       __IOHIDEventSystemClientSetPropertyForService, __IOHIDEventSystemClientUnregisterClientRecordsChangedBlock, 
                       __IOHIDEventSystemClientUnregisterClientRecordsChangedCallback, 
                       __IOHIDEventSystemClientUnregisterServiceRecordsChangedBlock, 
                       __IOHIDEventSystemClientUnregisterServiceRecordsChangedCallback, 
                       __IOHIDEventSystemConnectionAddNotification, __IOHIDEventSystemConnectionAddServices, 
                       __IOHIDEventSystemConnectionContainsService, __IOHIDEventSystemConnectionCopyEventCounts, 
                       __IOHIDEventSystemConnectionCopyEventLog, __IOHIDEventSystemConnectionCopyNotification, 
                       __IOHIDEventSystemConnectionCopyQueue, __IOHIDEventSystemConnectionCopyRecord, 
                       __IOHIDEventSystemConnectionCopyServices, __IOHIDEventSystemConnectionCreate, 
                       __IOHIDEventSystemConnectionCreatePrivate, __IOHIDEventSystemConnectionCreateVirtualService, 
                       __IOHIDEventSystemConnectionDispatchEvent, __IOHIDEventSystemConnectionDispatchEventForVirtualService, 
                       __IOHIDEventSystemConnectionEventFilterCompare, __IOHIDEventSystemConnectionFilterEvent, 
                       __IOHIDEventSystemConnectionGetDispatchQueue, __IOHIDEventSystemConnectionGetEventFilterPriority, 
                       __IOHIDEventSystemConnectionGetPID, __IOHIDEventSystemConnectionGetPort, 
                       __IOHIDEventSystemConnectionGetProperty, __IOHIDEventSystemConnectionGetReplyPort, 
                       __IOHIDEventSystemConnectionGetSystem, __IOHIDEventSystemConnectionInvalidate, 
                       __IOHIDEventSystemConnectionIsResponsive, __IOHIDEventSystemConnectionIsValid, 
                       __IOHIDEventSystemConnectionLogEvent, __IOHIDEventSystemConnectionPropertyChanged, 
                       __IOHIDEventSystemConnectionQueueStart, __IOHIDEventSystemConnectionQueueStop, 
                       __IOHIDEventSystemConnectionRecordClientChanged, __IOHIDEventSystemConnectionRecordServiceChanged, 
                       __IOHIDEventSystemConnectionRegisterDemuxCallback, __IOHIDEventSystemConnectionRegisterEventFilter, 
                       __IOHIDEventSystemConnectionRegisterPropertyChangedNotification, 
                       __IOHIDEventSystemConnectionRegisterRecordClientChanged, __IOHIDEventSystemConnectionRegisterRecordServiceChanged, 
                       __IOHIDEventSystemConnectionRegisterTerminationCallback, __IOHIDEventSystemConnectionReleasePrivate, 
                       __IOHIDEventSystemConnectionRemoveAllServices, __IOHIDEventSystemConnectionRemoveNotification, 
                       __IOHIDEventSystemConnectionRemoveService, __IOHIDEventSystemConnectionRemoveVirtualService, 
                       __IOHIDEventSystemConnectionScheduleAsync, __IOHIDEventSystemConnectionSetProperty, 
                       __IOHIDEventSystemConnectionSetQueue, __IOHIDEventSystemConnectionUnregisterEventFilter, 
                       __IOHIDEventSystemConnectionUnregisterPropertyChangedNotification, 
                       __IOHIDEventSystemConnectionUnregisterRecordClientChanged, 
                       __IOHIDEventSystemConnectionUnregisterRecordServiceChanged, 
                       __IOHIDEventSystemConnectionUnscheduleAsync, __IOHIDEventSystemConnectionVirtualServiceNotify, 
                       __IOHIDEventSystemCopyRecord, __IOHIDEventSystemDispatchEvent, 
                       __IOHIDEventSystemGetPropertyForConnection, __IOHIDEventSystemGetSession, 
                       __IOHIDEventSystemPropertyChanged, __IOHIDEventSystemRegisterEventFilter, 
                       __IOHIDEventSystemRegisterRecordClientChanged, __IOHIDEventSystemRegisterRecordServiceChanged, 
                       __IOHIDEventSystemRemoveConnection, __IOHIDEventSystemRemoveNotificationForConnection, 
                       __IOHIDEventSystemRemoveService, __IOHIDEventSystemRemoveServicesForConnection, 
                       __IOHIDEventSystemSetPropertyForConnection, __IOHIDEventSystemUnregisterEventFilter, 
                       __IOHIDEventSystemUnregisterRecordClientChanged, __IOHIDEventSystemUnregisterRecordServiceChanged, 
                       __IOHIDGetMonotonicTime, __IOHIDGetTimestampDelta, __IOHIDIsSerializable, 
                       __IOHIDLoadBundles, __IOHIDLoadServiceFilterBundles, __IOHIDLoadServicePluginBundles, 
                       __IOHIDLoadSessionFilterBundles, __IOHIDLog, __IOHIDLogCategory, 
                       __IOHIDObjectCreateInstance, __IOHIDObjectExtRetainCount, 
                       __IOHIDObjectIntRetainCount, __IOHIDObjectInternalRelease, 
                       __IOHIDObjectInternalReleaseCallback, __IOHIDObjectInternalRetain, 
                       __IOHIDObjectInternalRetainCallback, __IOHIDObjectRetainCount, 
                       __IOHIDPlugInInstanceCacheAdd, __IOHIDPlugInInstanceCacheClear, 
                       __IOHIDPlugInInstanceCacheIsEmpty, __IOHIDQueueCopyElements, 
                       __IOHIDSerialize, __IOHIDServiceAddConnection, __IOHIDServiceClientCacheProperties, 
                       __IOHIDServiceClientCopyUsageProp, __IOHIDServiceClientCreate, 
                       __IOHIDServiceClientCreatePrivate, __IOHIDServiceClientCreateVirtual, 
                       __IOHIDServiceClientDispatchServiceRemoval, __IOHIDServiceClientRefresh, 
                       __IOHIDServiceClientReleasePrivate, __IOHIDServiceClose, __IOHIDServiceContainsReportInterval, 
                       __IOHIDServiceContainsReportIntervalForClient, __IOHIDServiceCopyConnectionCache, 
                       __IOHIDServiceCopyConnectionIntervals, __IOHIDServiceCopyConnections, 
                       __IOHIDServiceCopyDispatchQueue, __IOHIDServiceCopyEventCounts, 
                       __IOHIDServiceCopyEventLog, __IOHIDServiceCopyFilterDebugInfoForClient, 
                       __IOHIDServiceCopyPropertiesForClient, __IOHIDServiceCopyPropertyForClient, 
                       __IOHIDServiceCopyProperyFromPlugin, __IOHIDServiceCopyServiceInfoForClient, 
                       __IOHIDServiceCopyServiceRecordForClient, __IOHIDServiceCreate, 
                       __IOHIDServiceCreatePrivate, __IOHIDServiceCreateVirtual, 
                       __IOHIDServiceCreateVirtualForConnection, __IOHIDServiceCurrentBatchInterval, 
                       __IOHIDServiceDispatchEvent, __IOHIDServiceGetEventDeadlineForClient, 
                       __IOHIDServiceGetOwner, __IOHIDServiceGetReportInterval, __IOHIDServiceGetReportIntervalForClient, 
                       __IOHIDServiceGetSenderID, __IOHIDServiceHidden, __IOHIDServiceInitVirtual, 
                       __IOHIDServiceIsInactive, __IOHIDServiceIsProtected, __IOHIDServiceOpen, 
                       __IOHIDServiceReleasePrivate, __IOHIDServiceRemoveConnection, 
                       __IOHIDServiceRemovePropertiesForClient, __IOHIDServiceScheduleAsync, 
                       __IOHIDServiceSetBatchIntervalForClient, __IOHIDServiceSetEventCallback, 
                       __IOHIDServiceSetEventDeadlineForClient, __IOHIDServiceSetMiscDebugDebugInfo, 
                       __IOHIDServiceSetPropertyForClient, __IOHIDServiceSetReportIntervalForClient, 
                       __IOHIDServiceSupportReportLatency, __IOHIDServiceTerminate, 
                       __IOHIDServiceUnscheduleAsync, __IOHIDSessionCreateActivityNotification, 
                       __IOHIDSessionCreatePrivate, __IOHIDSessionDispatchEvent, 
                       __IOHIDSessionGetPropertyForClient, __IOHIDSessionReleasePrivate, 
                       __IOHIDSessionSetPropertyForClient, __IOHIDSetFixedMouseLocation, 
                       __IOHIDSimpleQueueApplyBlock, __IOHIDSimpleQueueCreate, __IOHIDSimpleQueueDequeue, 
                       __IOHIDSimpleQueueEnqueue, __IOHIDSimpleQueuePeek, __IOHIDStringAppendIndendationAndFormat, 
                       __IOHIDUnserializeAndVMDealloc, __IOHIDUnserializeAndVMDeallocWithTypeID, 
                       __IOHIDValueCopyToElementValueHeader, __IOHIDValueCopyToElementValuePtr, 
                       __IOHIDValueCreateWithElementValuePtr, __IOHIDValueCreateWithStruct, 
                       __IOHIDValueCreateWithValue, __IOHIDValueGetFlags, __IOHIDVirtuaServiceClientGetEventSystemClient, 
                       __IOHIDVirtualServiceClientCopyEvent, __IOHIDVirtualServiceClientCopyMatchingEvent, 
                       __IOHIDVirtualServiceClientCopyProperty, __IOHIDVirtualServiceClientNotification, 
                       __IOHIDVirtualServiceClientSetOputputEvent, __IOHIDVirtualServiceClientSetProperty, 
                       __IOObjectCFRelease, __IOObjectCFRetain, __IOObjectConformsTo, 
                       __IOObjectCopyClass, __IOObjectGetClass, __IOReadBytesFromFile, 
                       __IOServiceGetAuthorizationID, __IOServiceSetAuthorizationID, 
                       __IOUSBDeviceDescriptionGetInfo, __IOWriteBytesToFile, __OSKextBasicFilesystemAuthentication, 
                       __OSKextCopyKernelRequests, __OSKextCreateFolderForCacheURL, 
                       __OSKextIdentifierHasApplePrefix, __OSKextReadCache, __OSKextReadFromIdentifierCacheForFolder, 
                       __OSKextSendResource, __OSKextSetAuthenticationFunction, __OSKextSetLoadAuditFunction, 
                       __OSKextSetPersonalityPatcherFunction, __OSKextSetStrictAuthentication, 
                       __OSKextSetStrictRecordingByLastOpened, __OSKextWriteCache, 
                       __OSKextWriteIdentifierCacheForKextsInDirectory, ___ConnectionFunctionPickBatchInterval, 
                       ___CopyRecordForCientFunction, ___FunctionApplierForParameters, 
                       ___GDBIOHIDEventSystemDump, ___IOAVClassMatching, ___IOAVCopyFirstMatchingIOAVObjectOfType, 
                       ___IOAVLogHandleDefault, ___IOAVLogHandleEvent, ___IODataQueueDequeue, 
                       ___IODataQueuePeek, ___IOHIDApplyPropertiesToDeviceFromDictionary, 
                       ___IOHIDApplyPropertyToDeviceSet, ___IOHIDDeviceGetRootKey, 
                       ___IOHIDDeviceGetUUIDKey, ___IOHIDDeviceGetUUIDString, ___IOHIDDeviceLoadProperties, 
                       ___IOHIDDeviceSaveProperties, ___IOHIDElementGetRootKey, ___IOHIDElementLoadProperties, 
                       ___IOHIDElementSaveProperties, ___IOHIDEventSystemClientFinalizeStateHandler, 
                       ___IOHIDEventSystemClientInitReplyPort, ___IOHIDEventSystemClientRefresh, 
                       ___IOHIDEventSystemClientRefreshServiceCallback, ___IOHIDEventSystemClientServiceReplaceCallback, 
                       ___IOHIDEventSystemConnectionActivityNotification, ___IOHIDEventSystemConnectionUpdateActivityState, 
                       ___IOHIDEventSystem_debug, ___IOHIDLoadElementSet, ___IOHIDManagerGetRootKey, 
                       ___IOHIDManagerLoadProperties, ___IOHIDManagerRegister, ___IOHIDManagerSaveProperties, 
                       ___IOHIDNotificationIntFinalize, ___IOHIDNotificationInvalidateCompletion, 
                       ___IOHIDNotificationRegister, ___IOHIDPlugInInstanceCacheApplier, 
                       ___IOHIDPropertyLoadDictionaryFromKey, ___IOHIDPropertyLoadFromKeyWithSpecialKeys, 
                       ___IOHIDPropertySaveToKeyWithSpecialKeys, ___IOHIDPropertySaveWithContext, 
                       ___IOHIDQueueRegister, ___IOHIDSaveDeviceSet, ___IOHIDSaveElementSet, 
                       ___IOHIDServiceClientCopyDebugDescription, ___IOHIDServiceCompleteInProgressEvents, 
                       ___IOHIDServiceCreateAndCopyConnectionCache, ___IOHIDServiceCreateVirtualNoInit, 
                       ___IOHIDServiceHandleCancelTimerTimeout, ___IOHIDServicePassiveMatchToFilterPlugin, 
                       ___IOHIDServicePickBatchInterval, ___IOHIDSessionActivityNotificationRelease, 
                       ___IOHIDSystemEnumerationQueueDidExecute, ___IOHIDSystemEnumerationQueueWillExecute, 
                       ___IOHIDTransactionRegister, ___IOHIDUserDeviceFinalizeStateHandler, 
                       ___IOHIDUserDeviceSerializeState, ___IOHIDUserDeviceStateHandler, 
                       ___IOHIDValueRegister, ___IOUSBDeviceDescriptionRegister, 
                       ___NotificationApplier, ___OSKextBundleIDCompare, ___OSKextCacheNeedsUpdate, 
                       ___OSKextCheckURL, ___OSKextClearHasAllDependenciesOnKext, 
                       ___OSKextCompareIdentifiers, ___OSKextCopyExecutableRelativePath, 
                       ___OSKextCreateCacheFileURL, ___OSKextCreateCompositeKey, 
                       ___OSKextCreateFromIdentifierCacheDict, ___OSKextCreateIdentifierCacheDict, 
                       ___OSKextCreateKextRequest, ___OSKextDeallocateMmapBuffer, 
                       ___OSKextGetBleedthroughFlag, ___OSKextLogDependencyGraphApplierFunction, 
                       ___OSKextLogKernelMessages, ___OSKextMapExecutable, ___OSKextProcessKextRequestResults, 
                       ___OSKextReadRegistryNumberProperty, ___OSKextRealize, ___OSKextRealizeKextsWithIdentifier, 
                       ___OSKextRemoveIdentifierCacheForKext, ___OSKextRemovePersonalities, 
                       ___OSKextSendKextRequest, ___OSKextSetLoadAddress, ___OSKextStatURL, 
                       ___OSKextStatURLsOrURL, ___OSKextURLIsSystemFolder, ___OSKextUUIDCallback, 
                       ___OSKextUnload, ___RegisterServiceWithSessionFunction, ___SetNumPropertyForService, 
                       ___VirtualServiceNotifier, ___VirtualServicesApplier, ___absPathOnVolume, 
                       ___hid_dispatch_queue_context_destructor, ___kOSKextDiagnosticsFlagAllImplemented, 
                       ___sOSKextDefaultLogFunction, ___sOSKextLogOutputFunction, 
                       ___uuid_callback, __copySleepPreventersList, __getPSDispatchQueue, 
                       __io_hideventsystem_clear_service_cache, __io_hideventsystem_copy_event_for_service, 
                       __io_hideventsystem_copy_matching_event_for_service, __io_hideventsystem_copy_matching_services, 
                       __io_hideventsystem_copy_properties_for_service, __io_hideventsystem_copy_property, 
                       __io_hideventsystem_copy_property_for_service, __io_hideventsystem_create_virtual_service, 
                       __io_hideventsystem_dispatch_event, __io_hideventsystem_dispatch_event_for_virtual_service, 
                       __io_hideventsystem_open, __io_hideventsystem_queue_create, 
                       __io_hideventsystem_queue_start, __io_hideventsystem_queue_stop, 
                       __io_hideventsystem_register_event_filter, __io_hideventsystem_register_property_changed_notification, 
                       __io_hideventsystem_register_record_client_changed_notification, 
                       __io_hideventsystem_register_record_service_changed_notification, 
                       __io_hideventsystem_release_notification, __io_hideventsystem_remove_virtual_service, 
                       __io_hideventsystem_service_conforms_to, __io_hideventsystem_set_element_value_for_service, 
                       __io_hideventsystem_set_properties, __io_hideventsystem_set_properties_for_service, 
                       __io_hideventsystem_unregister_event_filter, __io_hideventsystem_unregister_property_changed_notification, 
                       __io_hideventsystem_unregister_record_client_changed_notification, 
                       __io_hideventsystem_unregister_record_service_changed_notification, 
                       __io_kSCCompAnyRegex, __io_kSCDynamicStoreDomainState, __iohideventsystem_client_dispatch_client_records_changed, 
                       __iohideventsystem_client_dispatch_event_filter, __iohideventsystem_client_dispatch_notification_results, 
                       __iohideventsystem_client_dispatch_properties_changed, __iohideventsystem_client_dispatch_service_records_changed, 
                       __iohideventsystem_client_dispatch_service_removal, __iohideventsystem_client_dispatch_virtual_service_copy_property, 
                       __iohideventsystem_client_dispatch_virtual_service_notification, 
                       __iohideventsystem_client_dispatch_virtual_service_set_property, 
                       __iohideventsystem_client_subsystem, __iohideventsystem_copy_event_from_virtual_service, 
                       __iohideventsystem_copy_matching_event_from_virtual_service, 
                       __iohideventsystem_output_event_to_virtual_service, __iohideventsystem_subsystem, 
                       __isArray, __isDictionary, __isString, __pm_connect, __pm_disconnect, 
                       __servicePropertyCacheKeys, __servicePropertyCacheKeysCount, 
                       __systemPowerCallback, _comparePrefsToDefaults, _copyPreferencesForSrc, 
                       _createAsyncAssertion, _createCFStringForData, _createCFStringForPlist_new, 
                       _createUTF8CStringForCFString, _decodeIOPMUserIsActive, _defaultPropertyKeyValue, 
                       _defaultSettings, _ev_try_lock, _ev_unlock, _fat_iterator_close, 
                       _fat_iterator_file_end, _fat_iterator_file_start, _fat_iterator_find_arch, 
                       _fat_iterator_find_fat_arch, _fat_iterator_find_host_arch, 
                       _fat_iterator_for_data, _fat_iterator_is_iterable, _fat_iterator_next_arch, 
                       _fat_iterator_num_arches, _fat_iterator_open, _fat_iterator_reset, 
                       _gAsyncMode, _gIOCFPlugInInterfaceID, _gIOHIDDebugConfig, 
                       _gIOKitLibSerializeOptions, _gIOKitLibServerVersion, _gIOPMUserIsActive, 
                       _getEffectivePageSize, _getGenericPrefsPath, _getHostPrefsPath, 
                       _getMonotonicTime, _getPMQueue, _getUserActiveValidDict, _hid_dispatch_pthread_root_queue_create, 
                       _hid_dispatch_queue_create, _hid_dispatch_queue_create_with_context_destructor, 
                       _hid_dispatch_queue_release, _hid_pthread_attr_init, _hid_workloop_create, 
                       _initialSetup, _io_hideventsystem_clear_service_cache, _io_hideventsystem_copy_event_for_service, 
                       _io_hideventsystem_copy_matching_event_for_service, _io_hideventsystem_copy_matching_services, 
                       _io_hideventsystem_copy_properties_for_service, _io_hideventsystem_copy_property, 
                       _io_hideventsystem_copy_property_for_service, _io_hideventsystem_create_virtual_service, 
                       _io_hideventsystem_dispatch_event, _io_hideventsystem_dispatch_event_for_virtual_service, 
                       _io_hideventsystem_open, _io_hideventsystem_queue_create, 
                       _io_hideventsystem_queue_start, _io_hideventsystem_queue_stop, 
                       _io_hideventsystem_register_event_filter, _io_hideventsystem_register_property_changed_notification, 
                       _io_hideventsystem_register_record_client_changed_notification, 
                       _io_hideventsystem_register_record_service_changed_notification, 
                       _io_hideventsystem_release_notification, _io_hideventsystem_remove_virtual_service, 
                       _io_hideventsystem_service_conforms_to, _io_hideventsystem_set_element_value_for_service, 
                       _io_hideventsystem_set_properties, _io_hideventsystem_set_properties_for_service, 
                       _io_hideventsystem_unregister_event_filter, _io_hideventsystem_unregister_property_changed_notification, 
                       _io_hideventsystem_unregister_record_client_changed_notification, 
                       _io_hideventsystem_unregister_record_service_changed_notification, 
                       _io_pm_assertion_activity_aggregate, _io_pm_assertion_activity_log, 
                       _io_pm_assertion_copy_details, _io_pm_assertion_create, _io_pm_assertion_notify, 
                       _io_pm_assertion_retain_release, _io_pm_assertion_set_properties, 
                       _io_pm_cancel_repeat_events, _io_pm_change_sa_assertion_behavior, 
                       _io_pm_connection_acknowledge_event, _io_pm_connection_create, 
                       _io_pm_connection_release, _io_pm_connection_schedule_notification, 
                       _io_pm_ctl_assertion_type, _io_pm_declare_network_client_active, 
                       _io_pm_declare_system_active, _io_pm_declare_user_active, 
                       _io_pm_force_active_settings, _io_pm_get_capability_bits, 
                       _io_pm_get_uuid, _io_pm_get_value_int, _io_pm_hid_event_copy_history, 
                       _io_pm_hid_event_report_activity, _io_pm_last_wake_time, _io_pm_schedule_power_event, 
                       _io_pm_schedule_repeat_event, _io_pm_set_bt_wake_interval, 
                       _io_pm_set_debug_flags, _io_pm_set_dw_linger_interval, _io_pm_set_exception_limits, 
                       _io_pm_set_sleepservice_wake_time_cap, _io_pm_set_value_int, 
                       _io_ps_copy_powersources_info, _io_ps_new_pspowersource, _io_ps_release_pspowersource, 
                       _io_ps_update_pspowersource, _iohideventsystem_client_dispatch_client_records_changed, 
                       _iohideventsystem_client_dispatch_event_filter, _iohideventsystem_client_dispatch_notification_results, 
                       _iohideventsystem_client_dispatch_properties_changed, _iohideventsystem_client_dispatch_service_records_changed, 
                       _iohideventsystem_client_dispatch_service_removal, _iohideventsystem_client_dispatch_virtual_service_copy_property, 
                       _iohideventsystem_client_dispatch_virtual_service_notification, 
                       _iohideventsystem_client_dispatch_virtual_service_set_property, 
                       _iohideventsystem_client_server, _iohideventsystem_client_server_routine, 
                       _iohideventsystem_copy_event_from_virtual_service, _iohideventsystem_copy_matching_event_from_virtual_service, 
                       _iohideventsystem_output_event_to_virtual_service, _iohideventsystem_server, 
                       _iohideventsystem_server_routine, _iokit_user_client_trap, 
                       _isA_GenericPref, _isCrossLinking, _kIOAVCPCapabilitiesAll, 
                       _kIOAVCPCapabilitiesNone, _kIOAVCPConfigDefault, _kIOAVContentProtectionHDCP1, 
                       _kIOAVContentProtectionHDCP2, _kIOAVContentProtectionNone, 
                       _kIOAVDSCCapabilitiesNone, _kIOAVDSCParametersNone, _kIOEthernetHardwareAddress, 
                       _kIOHIDEventAttachmentSender, _kIOHIDEventSystemConnectionDispatchFilterWaitTimeoutMS, 
                       _kIOHIDFilterPluginArrayCallBacks, _kIOHIDServerConnectionRootQueue, 
                       _kIOHIDServiceCapsLockLEDKey, _kIOHIDServiceCapsLockLEDKey_Auto, 
                       _kIOHIDServiceCapsLockLEDKey_Inhibit, _kIOHIDServiceCapsLockLEDKey_Off, 
                       _kIOHIDServiceCapsLockLEDKey_On, _kIOHIDServiceEnumerationWorkloop, 
                       _kIOHIDServiceHiddenKey, _kIOHIDServiceInterruptWorkloop, 
                       _kIOMasterPortDefault, _kIOUserEthernetInterfaceMergeProperties, 
                       _kIOUserEthernetInterfaceRole, _kOSKextDependencyCircularReference, 
                       _kOSKextDependencyCompatibleVersionUndeclared, _kOSKextDependencyInauthentic, 
                       _kOSKextDependencyIndirectDependencyUnresolvable, _kOSKextDependencyIneligibleInSafeBoot, 
                       _kOSKextDependencyInvalid, _kOSKextDependencyLoadedCompatibleVersionUndeclared, 
                       _kOSKextDependencyLoadedIsIncompatible, _kOSKextDependencyMultipleVersionsDetected, 
                       _kOSKextDependencyNoCompatibleVersion, _kOSKextDependencyRawAndComponentKernel, 
                       _kOSKextDependencyUnavailable, _kOSKextDiagnosticBadPropertyListXMLKey, 
                       _kOSKextDiagnosticBadSystemPropertyKey, _kOSKextDiagnosticBundleIdentifierMismatchKey, 
                       _kOSKextDiagnosticBundleVersionMismatchKey, _kOSKextDiagnosticCodelessWithLibrariesKey, 
                       _kOSKextDiagnosticCompatibleVersionLaterThanVersionKey, _kOSKextDiagnosticDeclaresBothKernelAndKPIDependenciesKey, 
                       _kOSKextDiagnosticDeclaresNoKPIsWarningKey, _kOSKextDiagnosticDeclaresNonKPIDependenciesKey, 
                       _kOSKextDiagnosticDeprecatedPropertyKey, _kOSKextDiagnosticExecutableArchNotFoundKey, 
                       _kOSKextDiagnosticExecutableBadKey, _kOSKextDiagnosticExecutableMissingKey, 
                       _kOSKextDiagnosticFileAccessKey, _kOSKextDiagnosticFileNotFoundKey, 
                       _kOSKextDiagnosticIdentifierOrVersionTooLongKey, _kOSKextDiagnosticIneligibleInSafeBoot, 
                       _kOSKextDiagnosticInfoKeyIneligibleForDriverKit, _kOSKextDiagnosticInvalidSymlinkKey, 
                       _kOSKextDiagnosticKernelComponentNotInterfaceKey, _kOSKextDiagnosticKextIneligibleForDriverKit, 
                       _kOSKextDiagnosticMissingDesignatedKernelClass, _kOSKextDiagnosticMissingPropertyKey, 
                       _kOSKextDiagnosticNoExplicitKernelDependencyKey, _kOSKextDiagnosticNoFileKey, 
                       _kOSKextDiagnosticNonAppleKextDeclaresPrivateKPIDependencyKey, 
                       _kOSKextDiagnosticNonuniqueIOResourcesMatchKey, _kOSKextDiagnosticNotABundleKey, 
                       _kOSKextDiagnosticNotSignedKey, _kOSKextDiagnosticOSBundleRequiredValueIneligibleForDriverKit, 
                       _kOSKextDiagnosticOwnerPermissionKey, _kOSKextDiagnosticPersonalityHasDifferentBundleIdentifierKey, 
                       _kOSKextDiagnosticPersonalityHasNoBundleIdentifierKey, _kOSKextDiagnosticPersonalityNamesKextWithNoExecutableKey, 
                       _kOSKextDiagnosticPersonalityNamesNonloadableKextKey, _kOSKextDiagnosticPersonalityNamesUnknownKextKey, 
                       _kOSKextDiagnosticPropertyIsIllegalTypeKey, _kOSKextDiagnosticPropertyIsIllegalValueKey, 
                       _kOSKextDiagnosticRawKernelDependency, _kOSKextDiagnosticSharedExecutableAndExecutableKey, 
                       _kOSKextDiagnosticSharedExecutableKextMissingKey, _kOSKextDiagnosticStatFailureKey, 
                       _kOSKextDiagnosticSymlinkKey, _kOSKextDiagnosticThirdPartiesIneligibleForDriverKitOSBundleRequired, 
                       _kOSKextDiagnosticTypeWarningKey, _kOSKextDiagnosticURLConversionKey, 
                       _kOSKextDiagnosticUserExecutableAndExecutableKey, _kOSKextDiagnosticsAuthenticationKey, 
                       _kOSKextDiagnosticsBootLevelKey, _kOSKextDiagnosticsDependenciesKey, 
                       _kOSKextDiagnosticsDependencyNotOSBundleRequired, _kOSKextDiagnosticsInterfaceDependencyCount, 
                       _kOSKextDiagnosticsValidationKey, _kOSKextDiagnosticsWarningsKey, 
                       _kOSKextLoadNotification, _kOSKextUnloadNotification, _macho_find_dysymtab, 
                       _macho_find_section_numbered, _macho_find_source_version, 
                       _macho_find_symbol, _macho_find_symtab, _macho_find_uuid, 
                       _macho_get_section_by_name, _macho_get_section_by_name_64, 
                       _macho_get_segment_by_name, _macho_get_segment_by_name_64, 
                       _macho_remove_linkedit, _macho_scan_load_commands, _macho_swap, 
                       _macho_trim_linkedit, _macho_unswap, _offloadAssertions, _previouslySerialized, 
                       _printPList_new, _processAssertionTimeout, _processCheckAssertionsMsg, 
                       _processRemoteMsg, _processUserActivityMsg, _recordObjectInIDRefDictionary, 
                       _releaseAsyncAssertion, _roundPageCrossSafe, _roundPageCrossSafeFixedWidth, 
                       _sendAsyncAssertionMsg, _sendAsyncReleaseMsg, _sendUserActivityMsg, 
                       _setAsyncAssertionProperties, _setCrossLinkPageSize, _setDispatchQueue, 
                       _setPreferencesForSrc, _showPList_new ]
    objc-classes:    [ HIDConnection, HIDDevice, HIDElement, HIDEvent, HIDEventService, 
                       HIDServiceClient, HIDSession ]
    objc-ivars:      [ HIDConnection._connection, HIDDevice._device, HIDElement._element, 
                       HIDEvent._event, HIDEventService._service, HIDServiceClient._client, 
                       HIDSession._session ]
...

diff --git a/sources/HUDRootViewController.mm b/sources/HUDRootViewController.mm
index 1e00fa5..0e34e81 100644
--- a/sources/HUDRootViewController.mm
+++ b/sources/HUDRootViewController.mm
@@ -28,6 +28,7 @@
 
 #import "GetTask.h"
 #import "JRMem.h"
+//#import "huitu.h"
 
 #define NOTIFY_UI_LOCKSTATE    "com.apple.springboard.lockstate"
 #define NOTIFY_LS_APP_CHANGED  "com.apple.LaunchServices.ApplicationsChanged"
@@ -602,8 +603,11 @@ - (void)reloadUserDefaults
     } else {}
     // 方框
     if ([self keepInPlace]) {
-        
-    } else {}
+        // 游戏绘制
+//        drawUIView.hidden = NO;
+    } else {
+//        drawUIView.hidden = YES;
+    }
     // 美化
     if ([self singleLineMode] && !isMummyOpen) {
         if ([jrm mummy] < 0) { // 开启失败
@@ -631,7 +635,6 @@ - (BOOL)isLandscapeOrientation
     } else {
         isLandscape = UIInterfaceOrientationIsLandscape(_orientation);
     }
-    NSLog(@"[LLLL]:btn:isLandscapeOrientation:%d", isLandscape);
     return isLandscape;
 }
 
@@ -796,6 +799,8 @@ - (void)updateSpeedLabel
     //======LLLL=======
 }
 
+
+//huitu *drawUIView; // 游戏绘制窗口
 - (void)viewDidLoad
 {
     [super viewDidLoad];
@@ -848,10 +853,20 @@ - (void)viewDidLoad
     [self reloadUserDefaults];
     
     //=============LLLL=============
+    // 去掉阴影&隐藏其他组件
+    [_contentView setUserInteractionEnabled:NO];
+    _blurEffect = nil;
+    _containerView.hidden = YES;
+    _blurView.hidden = YES;
+    _speedLabel.hidden = YES;
+    _lockedView.hidden = YES;
+    
+    // 添加自己的绘制
     UIView *_LLLLView = [[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
     ScreenshotInvisibleContainer *_LLLLContainerView = [[ScreenshotInvisibleContainer alloc] initWithContent:_LLLLView];
     [_contentView addSubview:_LLLLContainerView.hiddenContainer];
     
+    // 测试文字
     int width = [UIScreen mainScreen].bounds.size.width;
     int height = [UIScreen mainScreen].bounds.size.height;
     UILabel *_testLabel = [[UILabel alloc] init];
@@ -860,15 +875,12 @@ - (void)viewDidLoad
     [_LLLLView addSubview:_testLabel];
     NSLog(@"[LLLL]:width:%d::height:%d", width, height);
     
-    // 去掉阴影&隐藏其他组件
-    [_contentView setUserInteractionEnabled:NO];
-    _blurEffect = nil;
-    _containerView.hidden = YES;
-    _blurView.hidden = YES;
-    _speedLabel.hidden = YES;
-    _lockedView.hidden = YES;
-    
-    
+//    // 游戏绘制
+//    if (drawUIView != nil) {
+//        drawUIView = [[huitu alloc] init];
+//        [_contentView addSubview:drawUIView];
+//        drawUIView.hidden = ![self keepInPlace];
+//    }
     
     //=============LLLL=============
 }

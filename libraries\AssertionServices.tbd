--- !tapi-tbd-v3
archs:                 [ armv7, armv7s, arm64, arm64e ]
platform:              ios
flags:                 [ flat_namespace ]
install-name:          /System/Library/PrivateFrameworks/AssertionServices.framework/AssertionServices
current-version:       505.100.7
compatibility-version: 1
objc-constraint:       retain_release
exports:
  - archs:                [ armv7, armv7s, arm64, arm64e ]
    symbols:              [ _AssertionServicesVersionNumber,
                            _AssertionServicesVersionString,
                            _BKLaunchdJobApplicationLabelPrefix,
                            _BKLaunchdJobSystemShellLabelPrefix,
                            _BKSApplicationNotificationStateChanged,
                            _BKSApplicationStateAll,
                            _BKSApplicationStateAppIsFrontmostKey,
                            _BKSApplicationStateDisplayIDKey,
                            _BKSApplicationStateExitCodeKey,
                            _BKSApplicationStateExitReasonKey,
                            _BKSApplicationStateExtensionKey,
                            _BKSApplicationStateHostPIDKey,
                            _BKSApplicationStateKey,
                            _BKSApplicationStateMostElevatedStateForPro<PERSON>IDK<PERSON>,
                            _BKSApplicationStateProcessIDKey,
                            _BKSApplicationStateRunningReasonAssertionIdentifierKey,
                            _BKSApplicationStateRunningReasonAssertionNotificationKey,
                            _BKSApplicationStateRunningReasonAssertionReasonKey,
                            _BKSApplicationStateRunningReasonsKey,
                            _BKSAssertionErrorDescription,
                            _BKSAssertionErrorDomain,
                            _BKSProcessAssertionBackgroundTimeRemaining,
                            _BKSProcessAssertionSetExpirationHandler,
                            _BKSProcessDiagnosticReportTypeDescription,
                            _BKSProcessErrorDomain,
                            _BKSProcessExtensionPropertyBundleID,
                            _BKSProcessVisibilityIsForeground,
                            _BKSProcessVisibilityMax,
                            _BKSTerminationAssertionActiveEfficacyForBundleIdentifier,
                            _BKSTerminationAssertionEfficacyDescription,
                            _BKSTerminationAssertionHeldForBundleIdentifier,
                            _BKSTerminationAssertionRegisterObserver,
                            _BKSTerminationAssertionUnregisterObserver,
                            _BKSWatchdogAssertionCreateForPID,
                            _BKSWatchdogAssertionGetTypeID,
                            _NSStringFromBKSProcessErrorCode,
                            _NSStringFromBKSProcessExitReason,
                            _NSStringFromBKSProcessTaskState,
                            _NSStringFromBKSProcessVisibility,
                            _RBSProcessLegacyStateDescriptor ]
    objc-classes:         [ BKSApplicationStateMonitor, BKSAssertion,
                            BKSLaunchdJobSpecification, BKSProcess,
                            BKSProcessAssertion, BKSProcessExitContext,
                            BKSTerminationAssertion,
                            BKSTerminationAssertionObserverManager,
                            BKSTerminationContext, BKSWorkspace ]
    objc-ivars:           [ BKSApplicationStateMonitor._elevatedPriority,
                            BKSApplicationStateMonitor._handler,
                            BKSApplicationStateMonitor._interestedAssertionReasons,
                            BKSApplicationStateMonitor._interestedBundleIDs,
                            BKSApplicationStateMonitor._interestedStates,
                            BKSApplicationStateMonitor._lock,
                            BKSApplicationStateMonitor._monitor,
                            BKSAssertion._acquisitionHandler,
                            BKSAssertion._attributes,
                            BKSAssertion._internalAssertion,
                            BKSAssertion._invalidationHandler,
                            BKSAssertion._lock, BKSAssertion._name,
                            BKSAssertion._target,
                            BKSLaunchdJobSpecification._arguments,
                            BKSLaunchdJobSpecification._bundleIdentifier,
                            BKSLaunchdJobSpecification._bundlePath,
                            BKSLaunchdJobSpecification._environment,
                            BKSLaunchdJobSpecification._executablePath,
                            BKSLaunchdJobSpecification._executionOptions,
                            BKSLaunchdJobSpecification._labelPrefix,
                            BKSLaunchdJobSpecification._machServices,
                            BKSLaunchdJobSpecification._managedPersona,
                            BKSLaunchdJobSpecification._standardError,
                            BKSLaunchdJobSpecification._standardOutput,
                            BKSProcess._accessoryAssertion,
                            BKSProcess._assertion, BKSProcess._audioAssertion,
                            BKSProcess._bootstrapped,
                            BKSProcess._connectedToExternalAccessories,
                            BKSProcess._delegate, BKSProcess._handle,
                            BKSProcess._identity, BKSProcess._jobSpec,
                            BKSProcess._lastExitContext, BKSProcess._lock,
                            BKSProcess._mediaAssertion, BKSProcess._monitor,
                            BKSProcess._nowPlayingWithAudio,
                            BKSProcess._processHandle,
                            BKSProcess._recordingAudio, BKSProcess._taskState,
                            BKSProcess._terminationReason,
                            BKSProcess._visibility,
                            BKSProcess._visibilityAssertion,
                            BKSProcess._workspaceLocked,
                            BKSProcessAssertion._flags,
                            BKSProcessAssertion._mediaPlaybackHackAssertion,
                            BKSProcessAssertion._reason,
                            BKSProcessExitContext._exitReason,
                            BKSTerminationAssertion._bundleIdentifier,
                            BKSTerminationAssertion._context,
                            BKSTerminationAssertion._efficacy,
                            BKSTerminationAssertionObserverManager._calloutQueue,
                            BKSTerminationAssertionObserverManager._launchPreventedBundleIDs,
                            BKSTerminationAssertionObserverManager._lock,
                            BKSTerminationAssertionObserverManager._monitor,
                            BKSTerminationAssertionObserverManager._monitorIsReady,
                            BKSTerminationAssertionObserverManager._observers,
                            BKSTerminationContext._exceptionCode,
                            BKSTerminationContext._explanation,
                            BKSTerminationContext._reportType ]
...

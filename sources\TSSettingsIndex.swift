//
//  TSSettingsIndex.swift
//  Trollspeed
//
//  Created by Less<PERSON> on 2024/1/25.
//

import Foundation

enum TSSettingsIndex: Int, CaseIterable {
    case passthroughMode = 0
    case keepInPlace
    case hideAtSnapshot
    case singleLineMode
    case usesInvertedColor
    case usesRotation
//    case usesLargeFont
//    case usesArrowPrefixes
//    case usesBitrate

    var key: String {
        switch self {
        case .passthroughMode:
            return HUDUserDefaultsKeyPassthroughMode
        case .keepInPlace:
            return HUDUserDefaultsKeyKeepInPlace
        case .hideAtSnapshot:
            return HUDUserDefaultsKeyHideAtSnapshot
        case .singleLineMode:
            return HUDUserDefaultsKeySingleLineMode
        case .usesInvertedColor:
            return HUDUserDefaultsKeyUsesInvertedColor
        case .usesRotation:
            return HUDUserDefaultsKeyUsesRotation
//        case .usesLargeFont:
//            return HUDUserDefaultsKeyUsesLargeFont
//        case .usesArrowPrefixes:
//            return HUDUserDefaultsKeyUsesArrowPrefixes
//        case .usesBitrate:
//            return HUDUserDefaultsKeyUsesBitrate
        }
    }

    var title: String {
        switch self {
        case .passthroughMode:  // 触摸穿透|过直播
            return NSLocalizedString("Pass-through", comment: "TSSettingsIndex")
        case .keepInPlace:      // 锁定位置|人物骨骼
            return NSLocalizedString("Keep In-place", comment: "TSSettingsIndex")
        case .hideAtSnapshot: // 过直播|射线
            return NSLocalizedString("Hide @snapshot", comment: "TSSettingsIndex")
        case .singleLineMode:   // 仅显示下载|人物名字
            return NSLocalizedString("Incoming Only", comment: "TSSettingsIndex")
        case .usesInvertedColor: // 人物上色
            return NSLocalizedString("Appearance", comment: "TSSettingsIndex")
        case .usesRotation: // |美化
            return NSLocalizedString("Landscape", comment: "TSSettingsIndex")
//        case .usesLargeFont:
//            return NSLocalizedString("Size", comment: "TSSettingsIndex")
//        case .usesArrowPrefixes:
//            return NSLocalizedString("Prefixes", comment: "TSSettingsIndex")
//        case .usesBitrate:
//            return NSLocalizedString("Unit", comment: "TSSettingsIndex")
        }
    }

    func subtitle(highlighted: Bool, restartRequired: Bool) -> String {
        switch self {
        case .passthroughMode:
            if false && restartRequired {
                return NSLocalizedString("Re-open to apply", comment: "TSSettingsIndex")
            } else {
                return highlighted ? NSLocalizedString("ON", comment: "TSSettingsIndex") : NSLocalizedString("OFF", comment: "TSSettingsIndex")
            }
        case .keepInPlace: fallthrough
        case .hideAtSnapshot: fallthrough
        case .singleLineMode:
            return highlighted ? NSLocalizedString("ON", comment: "TSSettingsIndex") : NSLocalizedString("OFF", comment: "TSSettingsIndex")
        case .usesInvertedColor:
            return highlighted ? NSLocalizedString("ON", comment: "TSSettingsIndex") : NSLocalizedString("OFF", comment: "TSSettingsIndex")
        case .usesRotation:
            return highlighted ? NSLocalizedString("ON", comment: "TSSettingsIndex") : NSLocalizedString("OFF", comment: "TSSettingsIndex")
//        case .usesLargeFont:
//            return highlighted ? NSLocalizedString("Large", comment: "TSSettingsIndex") : NSLocalizedString("Standard", comment: "TSSettingsIndex")
//        case .usesArrowPrefixes:
//            return highlighted ? NSLocalizedString("↑↓", comment: "TSSettingsIndex") : NSLocalizedString("▲▼", comment: "TSSettingsIndex")
//        case .usesBitrate:
//            return highlighted ? NSLocalizedString("b/s", comment: "TSSettingsIndex") : NSLocalizedString("B/s", comment: "TSSettingsIndex")
        }
    }
}

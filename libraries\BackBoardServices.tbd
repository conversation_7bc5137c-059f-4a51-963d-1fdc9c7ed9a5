--- !tapi-tbd-v3
archs:                 [ armv7, armv7s, arm64, arm64e ]
platform:              ios
flags:                 [ flat_namespace ]
install-name:          /System/Library/PrivateFrameworks/BackBoardServices.framework/BackBoardServices
current-version:       1
compatibility-version: 1
objc-constraint:       retain_release
exports:
  - archs:                [ armv7, armv7s, arm64, arm64e ]
    re-exports:           [ /System/Library/PrivateFrameworks/AssertionServices.framework/AssertionServices ]
    symbols:              [ _BKAccelerometer_server,
                            _BKAccelerometer_server_routine,
                            _BKCreateMIGServerSource,
                            _BKCreateMIGServerSourceWithContext,
                            _BKDefaultBrightness, _B<PERSON><PERSON>efaultKeyALSEnabled,
                            _BKGetContextForCalloutCurrentMIGServerSource,
                            _BKGetMachPortForMIGServerSource,
                            _BKHIDServicesCancelPhysicalButtonEvents,
                            _BKHIDServicesCopyAuthenticationMessageKey,
                            _BKHIDServicesGetCurrentDeviceOrientation,
                            _BKHIDServicesGetNonFlatDeviceOrientation,
                            _BKLogAlternateSystemApp, _BKLogBacklight,
                            _BKLogCommon, _BKLogDetailed, _BKLogDisplay,
                            _BKLogDisplayArchive, _BKLogEventDelivery,
                            _BKLogFenceArbiter, _BKLogFenceObserver,
                            _BKLogFenceWorkspace, _BKLogFenceWorkspaceVerbose,
                            _BKLogIdleTimer, _BKLogMousePointer,
                            _BKLogOrientationClient, _BKLogOrientationDevice,
                            _BKLogOrientationGlobal, _BKLogOrientationHostUI,
                            _BKLogTouchDeliveryObserver, _BKLogUISensor,
                            _BKLoggingSubsystem, _BKMouseInterpolationPointer,
                            _BKMouseInterpolationRotation,
                            _BKMouseInterpolationScale,
                            _BKMouseInterpolationScroll,
                            _BKMouseInterpolationTranslation,
                            _BKNSStringFromIOHIDEventPhase,
                            _BKSActivateForEventOptionTypeBackgroundContentFetching,
                            _BKSApplicationTerminationReasonDescription,
                            _BKSApplicationTerminationReasonIsGraceful,
                            _BKSDebugOptionKeyArguments,
                            _BKSDebugOptionKeyCancelDebugOnNextLaunch,
                            _BKSDebugOptionKeyDebugOnNextLaunch,
                            _BKSDebugOptionKeyDisableASLR,
                            _BKSDebugOptionKeyEnvironment,
                            _BKSDebugOptionKeyStandardErrorPath,
                            _BKSDebugOptionKeyStandardOutPath,
                            _BKSDebugOptionKeyWaitForDebugger,
                            _BKSDisplayBrightnessCurveGetCurrent,
                            _BKSDisplayBrightnessCurveSet,
                            _BKSDisplayBrightnessGetCurrent,
                            _BKSDisplayBrightnessRestoreSystemBrightness,
                            _BKSDisplayBrightnessSet,
                            _BKSDisplayBrightnessSetAutoBrightnessEnabled,
                            _BKSDisplayBrightnessSetWithImplicitTransaction,
                            _BKSDisplayBrightnessTransactionCreate,
                            _BKSDisplayBrightnessTransactionGetTypeID,
                            _BKSDisplayServicesApplyRenderOverlay,
                            _BKSDisplayServicesArchiveWithOptionsAndCompletion,
                            _BKSDisplayServicesDismissInterstitialRenderOverlay,
                            _BKSDisplayServicesDisplayIsTethered,
                            _BKSDisplayServicesDrawPersistentScreenSnapshot,
                            _BKSDisplayServicesFreezeRenderOverlay,
                            _BKSDisplayServicesGetExternalDisplayScale,
                            _BKSDisplayServicesGetMainScreenInfo,
                            _BKSDisplayServicesGetRenderOverlayDismissActions,
                            _BKSDisplayServicesGetRenderOverlayForDisplay,
                            _BKSDisplayServicesGetTVSignalTypeIsDigital,
                            _BKSDisplayServicesIsFlipBookEnabled,
                            _BKSDisplayServicesIsScreenDisabled,
                            _BKSDisplayServicesRemoveRenderOverlay,
                            _BKSDisplayServicesServerPort,
                            _BKSDisplayServicesSetCalibrationPhase,
                            _BKSDisplayServicesSetCloneMirroringMode,
                            _BKSDisplayServicesSetCloneRotationDisabled,
                            _BKSDisplayServicesSetFlipBookEnabled,
                            _BKSDisplayServicesSetRefreshRateSettings,
                            _BKSDisplayServicesSetReplayCloneContexts,
                            _BKSDisplayServicesSetScreenBlanked,
                            _BKSDisplayServicesSetScreenDisabled,
                            _BKSDisplayServicesSetTetheredOrientationNotificationsDisabled,
                            _BKSDisplayServicesSetVirtualDisplayClientPID,
                            _BKSDisplayServicesStart,
                            _BKSDisplayServicesTearDownPersistentScreenSnapshot,
                            _BKSDisplayServicesTetherPrefsNeedImmediateUpdate,
                            _BKSDisplayServicesUpdateMirroredDisplayOrientationWithInterfaceOrientation,
                            _BKSDisplayServicesUpdateTetheredDisplayOrientationIfNecessaryWithInterfaceOrientation,
                            _BKSDisplayServicesWillUnblank,
                            _BKSDisplaySetSecureMode,
                            _BKSEventFocusDeferralPropertyUnspecifiedContextID,
                            _BKSEventFocusDeferralPropertyUnspecifiedDisplayUUID,
                            _BKSFenceArbiterServiceName, _BKSFenceLogApp,
                            _BKSFenceLogArbiter, _BKSFenceLogObserver,
                            _BKSFenceLogTrace, _BKSFenceLogWorkspace,
                            _BKSHIDDigitizerEventIsFirstTouchDown,
                            _BKSHIDEventAttributeDataWithExpectedClass,
                            _BKSHIDEventContainsUpdates,
                            _BKSHIDEventCopyDisplayIDFromDigitizerEvent,
                            _BKSHIDEventCopyDisplayIDFromEvent,
                            _BKSHIDEventDeliveryObserverBSServiceName,
                            _BKSHIDEventDescription,
                            _BKSHIDEventDigitizerDetachTouches,
                            _BKSHIDEventDigitizerDetachTouchesWithIdentifiers,
                            _BKSHIDEventDigitizerDetachTouchesWithIdentifiersAndPolicy,
                            _BKSHIDEventDigitizerGetTouchIdentifier,
                            _BKSHIDEventDigitizerGetTouchLocus,
                            _BKSHIDEventDigitizerGetTouchUserIdentifier,
                            _BKSHIDEventDigitizerSetTouchOffset,
                            _BKSHIDEventDigitizerSetTouchRoutingPolicy,
                            _BKSHIDEventEnumerateChildEvents,
                            _BKSHIDEventEnumerateUpdatesWithBlock,
                            _BKSHIDEventGetBaseAttributes,
                            _BKSHIDEventGetButtonIsCancelledFromButtonEvent,
                            _BKSHIDEventGetClientIdentifier,
                            _BKSHIDEventGetClientPid,
                            _BKSHIDEventGetConciseDescription,
                            _BKSHIDEventGetContextIDFromDigitizerEvent,
                            _BKSHIDEventGetContextIDFromEvent,
                            _BKSHIDEventGetDigitizerAttributes,
                            _BKSHIDEventGetHitTestPointFromDigitizerEventForPathEvent,
                            _BKSHIDEventGetInitialTouchTimestampFromDigitizerEvent,
                            _BKSHIDEventGetIsSystemAppEventFromEvent,
                            _BKSHIDEventGetIsSystemGestureStateChangeFromDigitizerEvent,
                            _BKSHIDEventGetMaximumForceFromDigitizerEvent,
                            _BKSHIDEventGetPointFromDigitizerEvent,
                            _BKSHIDEventGetPointerAttributes,
                            _BKSHIDEventGetPrecisePointFromDigitizerEventForPathEvent,
                            _BKSHIDEventGetSmartCoverStateFromEvent,
                            _BKSHIDEventGetSourceFromKeyboardEvent,
                            _BKSHIDEventGetSystemGestureStatusFromDigitizerEvent,
                            _BKSHIDEventGetTouchStreamIdentifier,
                            _BKSHIDEventGetZGradientFromDigitizerEventForPathEvent,
                            _BKSHIDEventMatchingPredicate,
                            _BKSHIDEventRegisterEventCallback,
                            _BKSHIDEventRegisterEventCallbackOnRunLoop,
                            _BKSHIDEventSendToApplicationWithBundleID,
                            _BKSHIDEventSendToApplicationWithBundleIDAndPid,
                            _BKSHIDEventSendToApplicationWithBundleIDAndPidAndFollowingFocusChain,
                            _BKSHIDEventSendToFocusedProcess,
                            _BKSHIDEventSendToProcess,
                            _BKSHIDEventSendToProcessAndFollowDeferringRules,
                            _BKSHIDEventSendToResolvedProcessForDeferringEnvironment,
                            _BKSHIDEventSetAttributes,
                            _BKSHIDEventSetBaseAttributes,
                            _BKSHIDEventSetDigitizerAttributes,
                            _BKSHIDEventSetDigitizerInfo,
                            _BKSHIDEventSetDigitizerInfoWithSubEventInfoAndTouchStreamIdentifier,
                            _BKSHIDEventSetDigitizerInfoWithSubEventInfos,
                            _BKSHIDEventSetDigitizerInfoWithTouchStreamIdentifier,
                            _BKSHIDEventSetPointerAttributes,
                            _BKSHIDEventSetSimpleDeliveryInfo,
                            _BKSHIDEventSetSimpleInfo,
                            _BKSHIDEventSetSmartCoverState,
                            _BKSHIDEventSourceStringName,
                            _BKSHIDKeyboardGetDeviceProperties,
                            _BKSHIDKeyboardIsCapsLockLightOn,
                            _BKSHIDKeyboardSetCapsLockDelayOverride,
                            _BKSHIDKeyboardSetCapsLockRomanSwitchMode,
                            _BKSHIDKeyboardSetLayout,
                            _BKSHIDServiceConnectionBSServiceDomainName,
                            _BKSHIDServiceConnectionMachName,
                            _BKSHIDServicesAmbientLightSensorDisableAutoBrightness,
                            _BKSHIDServicesAmbientLightSensorEnableAutoBrightness,
                            _BKSHIDServicesAmbientLightSensorExists,
                            _BKSHIDServicesCancelButtonEventsFromSenderID,
                            _BKSHIDServicesCancelTouchesOnAllDisplays,
                            _BKSHIDServicesCancelTouchesOnDisplay,
                            _BKSHIDServicesCancelTouchesOnMainDisplay,
                            _BKSHIDServicesCancelTouchesWithIdentifiers,
                            _BKSHIDServicesCapsLockKeyHasLanguageSwitchLabel,
                            _BKSHIDServicesClaimGenericGestureFocus,
                            _BKSHIDServicesDeviceHasHighFrequencyDigitizer,
                            _BKSHIDServicesGetAuthenticationMessageKey,
                            _BKSHIDServicesGetAuthenticationMessageKeys,
                            _BKSHIDServicesGetBacklightFactor,
                            _BKSHIDServicesGetCALayerTransform,
                            _BKSHIDServicesGetDeviceBacklightArchitectureVersion,
                            _BKSHIDServicesGetHardwareKeyboardLanguage,
                            _BKSHIDServicesGetHumanPresenceStatus,
                            _BKSHIDServicesGetObjectInProximityIgnoresTouches,
                            _BKSHIDServicesGetObjectWithinProximity,
                            _BKSHIDServicesGetRingerState,
                            _BKSHIDServicesIsCapsLockLightOn,
                            _BKSHIDServicesIsOrientationLockedWithOrientation,
                            _BKSHIDServicesIsSmartCoverClosed,
                            _BKSHIDServicesLastUserEventTime,
                            _BKSHIDServicesLockOrientation,
                            _BKSHIDServicesModifierKeysRemappingPreferenceKey,
                            _BKSHIDServicesNotifyOnNextUserEvent,
                            _BKSHIDServicesProductIdentifierFromServiceProperties,
                            _BKSHIDServicesProximityDetectionActive,
                            _BKSHIDServicesProximityDetectionModeIsValid,
                            _BKSHIDServicesRequestEstimatedProximityEvents,
                            _BKSHIDServicesRequestProximityDetectionMode,
                            _BKSHIDServicesRequestProximityStatusEvent,
                            _BKSHIDServicesResetProximityCalibration,
                            _BKSHIDServicesResetUserEventTimer,
                            _BKSHIDServicesSafeToResetIdleTimer,
                            _BKSHIDServicesServerPort,
                            _BKSHIDServicesSetAccelerometerClientEventsEnabled,
                            _BKSHIDServicesSetAuthenticatedKeyCommands,
                            _BKSHIDServicesSetBacklightFactorPending,
                            _BKSHIDServicesSetBacklightFactorWithFadeDuration,
                            _BKSHIDServicesSetBacklightFactorWithFadeDurationAsync,
                            _BKSHIDServicesSetBacklightFactorWithFadeDurationSilently,
                            _BKSHIDServicesSetBacklightFactorWithFadeDurationSilentlyAsync,
                            _BKSHIDServicesSetBacklightFeatures,
                            _BKSHIDServicesSetCapsLockDelayOverride,
                            _BKSHIDServicesSetCapsLockRomanSwitchMode,
                            _BKSHIDServicesSetDeviceInterfaceOrientation,
                            _BKSHIDServicesSetDeviceOrientationForAutomation,
                            _BKSHIDServicesSetHIDUILockedState,
                            _BKSHIDServicesSetHIDUILockedStateWithSource,
                            _BKSHIDServicesSetHardwareKeyboardLayout,
                            _BKSHIDServicesSetHitTestRegionsForDisplay,
                            _BKSHIDServicesSetMinimumBrightnessLevelWithFadeDuration,
                            _BKSHIDServicesSetObjectInProximityIgnoresTouches,
                            _BKSHIDServicesSetOrientationClient,
                            _BKSHIDServicesSetProximityEventsClient,
                            _BKSHIDServicesSetStackshotCombos,
                            _BKSHIDServicesSetTouchHand,
                            _BKSHIDServicesSetTouchPadAvailability,
                            _BKSHIDServicesSetVolumeAndPowerButtonPrecedence,
                            _BKSHIDServicesSetWristState,
                            _BKSHIDServicesUnlockOrientation,
                            _BKSHIDServicesUserEventTimerIntervalForever,
                            _BKSHIDSetDiscreteDispatchingRules,
                            _BKSHIDSetEventDeferringRules, _BKSHIDSetKeyCommands,
                            _BKSHIDSetKeyCommandsDispatchingRules,
                            _BKSHIDSetKeyCommandsRegistrations,
                            _BKSOpenApplicationErrorCodeToString,
                            _BKSOpenApplicationOptionKeyActivateAsClassic,
                            _BKSOpenApplicationOptionKeyActivateForEvent,
                            _BKSOpenApplicationOptionKeyActivateSuspended,
                            _BKSOpenApplicationOptionKeyDebuggingOptions,
                            _BKSOpenApplicationOptionKeyIsSensitiveURL,
                            _BKSOpenApplicationOptionKeyLaunchImageName,
                            _BKSOpenApplicationOptionKeyLaunchOrigin,
                            _BKSOpenApplicationOptionKeyPayloadAnnotation,
                            _BKSOpenApplicationOptionKeyPayloadOptions,
                            _BKSOpenApplicationOptionKeyPayloadURL,
                            _BKSOpenApplicationOptionKeyServiceAvailabilityTimeout,
                            _BKSOpenApplicationOptionKeyUnlockDevice,
                            _BKSRestartActionOptionsDescription,
                            _BKSSystemApplicationMessageKeyActions,
                            _BKSSystemApplicationMessageKeyBundleID,
                            _BKSSystemApplicationMessageKeyBundlePath,
                            _BKSSystemApplicationMessageKeyIdleSleepInterval,
                            _BKSSystemApplicationMessageKeyIsAlive,
                            _BKSSystemApplicationMessageKeyLaunchJobLabel,
                            _BKSSystemApplicationMessageKeyMessageType,
                            _BKSSystemApplicationServiceName,
                            _BKSTerminateApplicationForReasonAndReportWithDescription,
                            _BKSTerminateApplicationGroupForReasonAndReportWithDescription,
                            _BKSTouchDeliveryObservationBSServiceName,
                            _BKSTouchDeliveryPolicyServerGetProxyWithErrorHandler,
                            _BKSTouchDeliveryPolicyServerMachServiceName,
                            _BKSWatchdogGetIsAlive, _BKSWatchdogServerPort,
                            _BSDescriptionStreamAppendBKSKeyModifierFlags,
                            _NSStringFromBKSButtonHapticAssetType,
                            _NSStringFromBKSHIDEventAuthenticationStatus,
                            _NSStringFromBKSHIDEventHardwareType,
                            _NSStringFromBKSHIDEventPointerAttributeOptions,
                            _NSStringFromBKSHIDEventPointerAttributeState,
                            _NSStringFromBKSHIDEventScreenEdgeMask,
                            _NSStringFromBKSHIDEventSmartCoverState,
                            _NSStringFromBKSHIDEventSource,
                            _NSStringFromBKSHIDServicesHumanPresenceStatus,
                            _NSStringFromBKSHIDServicesProximityDetectionMode,
                            _NSStringFromBKSHIDServicesUserEventTimerMode,
                            _NSStringFromBKSHIDTouchLocus,
                            _NSStringFromBKSHIDUILockSource,
                            _NSStringFromBKSHIDUILockState,
                            _NSStringFromBKSHIDUISensorChangeSource,
                            _NSStringFromBKSHapticAssetIdentifier,
                            _NSStringFromBKSHapticAssetSpeed,
                            _NSStringFromBKSKeyModifierFlags,
                            _NSStringFromBKSTouchStreamIdentifier,
                            __BKSDisplayApplyRenderOverlay,
                            __BKSDisplayDismissInterstitialRenderOverlay,
                            __BKSDisplayDisplayIsTethered,
                            __BKSDisplayFreezeRenderOverlay,
                            __BKSDisplayGetMainScreenInfo,
                            __BKSDisplayGetRenderOverlayDismissActions,
                            __BKSDisplayIsDisabled,
                            __BKSDisplayIsFlipBookEnabled,
                            __BKSDisplayRemoveRenderOverlay,
                            __BKSDisplayRenderOverlay,
                            __BKSDisplaySetCalibrationPhase,
                            __BKSDisplaySetCloneMirroringMode,
                            __BKSDisplaySetCloneRotationDisabled,
                            __BKSDisplaySetDisabled,
                            __BKSDisplaySetFlipBookEnabled,
                            __BKSDisplaySetRefreshRateSettings,
                            __BKSDisplaySetReplayCloneContextIDs,
                            __BKSDisplaySetScreenBlanked,
                            __BKSDisplaySetSecureMode,
                            __BKSDisplaySetTetheredOrientationNotificationsDisabled,
                            __BKSDisplaySetVirtualDisplayClientPID,
                            __BKSDisplayTetherPrefsNeedImmediateUpdate,
                            __BKSDisplayUpdateMirroredDisplayOrientationWithInterfaceOrientation,
                            __BKSDisplayUpdateTetheredDisplayOrientationIfNecessaryWithInterfaceOrientation,
                            __BKSDisplayWillUnblank,
                            __BKSHIDAmbientLightSensorDisableAutoBrightness,
                            __BKSHIDAmbientLightSensorEnableAutoBrightness,
                            __BKSHIDAmbientLightSensorExists,
                            __BKSHIDApplyButtonDefinitions,
                            __BKSHIDBeginDisplayBrightnessTransaction,
                            __BKSHIDCancelButtonEventsFromSenderID,
                            __BKSHIDCancelPhysicalButtonEvents,
                            __BKSHIDCancelTouchesOnAllDisplays,
                            __BKSHIDCancelTouchesOnDisplay,
                            __BKSHIDCancelTouchesWithIdentifiers,
                            __BKSHIDClaimGenericGestureFocus,
                            __BKSHIDDigitizerTouchDetach,
                            __BKSHIDDigitizerTouchSetOffset,
                            __BKSHIDDigitizerTouchSetRoutingPolicy,
                            __BKSHIDEventGetConciseDescriptionButton,
                            __BKSHIDEventGetConciseDescriptionGenericGesture,
                            __BKSHIDEventGetConciseDescriptionKeyboard,
                            __BKSHIDEventGetConciseDescriptionPointer,
                            __BKSHIDEventGetConciseDescriptionProximity,
                            __BKSHIDEventGetConciseDescriptionRotation,
                            __BKSHIDEventGetConciseDescriptionScale,
                            __BKSHIDEventGetConciseDescriptionScroll,
                            __BKSHIDEventGetConciseDescriptionTranslation,
                            __BKSHIDEventGetEventInfoDescription,
                            __BKSHIDEventGetSubEventInfoFromDigitierEventForPathEvent,
                            __BKSHIDEventSetRedirectInfo,
                            __BKSHIDFlushDisplayBrightnessUpdates,
                            __BKSHIDGetBacklightFactor,
                            __BKSHIDGetCALayerTransform,
                            __BKSHIDGetCurrentDeviceOrientation,
                            __BKSHIDGetCurrentDisplayBrightness,
                            __BKSHIDGetCurrentDisplayBrightnessCurve,
                            __BKSHIDGetDeviceBacklightArchitectureVersion,
                            __BKSHIDGetEventAuthenticationMessageKeys,
                            __BKSHIDGetHumanPresenceStatus,
                            __BKSHIDGetKeyboardDeviceProperties,
                            __BKSHIDGetLastUserEventTime,
                            __BKSHIDGetNonFlatDeviceOrientation,
                            __BKSHIDGetObjectInProximityIgnoresTouches,
                            __BKSHIDGetObjectWithinProximity,
                            __BKSHIDGetProximityDetectionActive,
                            __BKSHIDGetRingerState,
                            __BKSHIDGetUISensorCharacteristics,
                            __BKSHIDIsCapsLockLightOn,
                            __BKSHIDIsOrientationLockedWithOrientation,
                            __BKSHIDIsSmartCoverClosed, __BKSHIDLockOrientation,
                            __BKSHIDNotifyOnNextUserEvent, __BKSHIDPlayHaptic,
                            __BKSHIDPostTouchAnnotations,
                            __BKSHIDRequestEstimatedProximityEvents,
                            __BKSHIDRequestProximityStatusEvent,
                            __BKSHIDRequestUISensorMode,
                            __BKSHIDResetProximityCalibration,
                            __BKSHIDResetUserEventTimer,
                            __BKSHIDRestoreSystemDisplayBrightness,
                            __BKSHIDSafeToResetIdleTimer,
                            __BKSHIDServicesApplyButtonDefinitions,
                            __BKSHIDServicesPlayHaptic,
                            __BKSHIDServicesPostTouchAnnotations,
                            __BKSHIDSetAccelerometerClientEventsEnabled,
                            __BKSHIDSetAuthenticatedKeyCommands,
                            __BKSHIDSetAutoDisplayBrightnessEnabled,
                            __BKSHIDSetBacklightFactorPending,
                            __BKSHIDSetBacklightFactorWithFadeDuration,
                            __BKSHIDSetBacklightFactorWithFadeDurationAsync,
                            __BKSHIDSetBacklightFeatures,
                            __BKSHIDSetCapsLockDelayOverride,
                            __BKSHIDSetCapsLockRomanSwitchMode,
                            __BKSHIDSetDeviceInterfaceOrientation,
                            __BKSHIDSetDeviceOrientationForAutomation,
                            __BKSHIDSetDiscreteDispatchingRules,
                            __BKSHIDSetDisplayBrightnessCurveValue,
                            __BKSHIDSetDisplayBrightnessValue,
                            __BKSHIDSetDisplayBrightnessWithImplicitTransaction,
                            __BKSHIDSetEventDeferringRulesForClient,
                            __BKSHIDSetHardwareKeyboardLayout,
                            __BKSHIDSetHitTestRegionsForDisplay,
                            __BKSHIDSetKeyCommandsDispatchingRules,
                            __BKSHIDSetKeyCommandsRegistrations,
                            __BKSHIDSetMinimumBrightnessLevelWithFadeDuration,
                            __BKSHIDSetObjectInProximityIgnoresTouches,
                            __BKSHIDSetOrientationClient,
                            __BKSHIDSetOrientationClientEventsEnabled,
                            __BKSHIDSetTouchHand, __BKSHIDSetWristState,
                            __BKSHIDTouchSetAuthenticationSpecifications,
                            __BKSHIDTouchStreamCreate,
                            __BKSHIDTouchStreamInvalidate,
                            __BKSHIDTouchStreamSetEventDispatchMode,
                            __BKSHIDUnlockOrientation,
                            __BKSHIDVerifyEventAuthenticationMessage,
                            __BKSWatchdogGetIsAlive,
                            __BKSWatchdogSetServerWrapper,
                            __BKXXBKAccelerometer_subsystem,
                            __BKXXDeliverAccelerometerEvent,
                            __RedirectEventToClient,
                            _kBKSDisplayServerDiedNotification,
                            _kBKSFenceArbiterMessageKey_Port,
                            _kBKSFenceArbiterMessageKey_Type,
                            _kBKSHIDServerDiedNotification,
                            _kBKSHIDServicesSafeToSetIdleTimerNotification,
                            _kBKSHIDServicesStackshotTaken,
                            _kBKSHIDServicesTouchPadAvailabilityNotification,
                            _kBKSHIDServicesUserEventIdled,
                            _kBKSHIDServicesUserEventOccurred,
                            _kBKSHIDServicesUserEventPresenceExpired,
                            _kBKSHIDServicesUserEventPresent,
                            _kBKSHIDServicesUserEventUnIdled ]
    objc-classes:         [ BKSAbstractDefaults, BKSAccelerometer,
                            BKSAlternateSystemApp, BKSAnimationFenceAssertion,
                            BKSAnimationFenceHandle, BKSAnimationFenceObserver,
                            BKSApplicationDataStore, BKSBacklightFeatures,
                            BKSButtonHapticsController,
                            BKSButtonHapticsDefinition,
                            BKSCAAnimationFenceHandle, BKSContextRelativePoint,
                            BKSDefaults,
                            BKSDisplayInterstitialRenderOverlayDismissAction,
                            BKSDisplayProgressIndicatorProperties,
                            BKSDisplayRenderOverlay,
                            BKSDisplayRenderOverlayDescriptor,
                            BKSEventFocusDeferral,
                            BKSEventFocusDeferralProperties,
                            BKSEventFocusManager, BKSExternalDefaults,
                            BKSHIDAuthenticatedKeyCommandSpecification,
                            BKSHIDEventAuthenticationKey,
                            BKSHIDEventAuthenticationKeyRetentionPolicy,
                            BKSHIDEventAuthenticationKeyRing,
                            BKSHIDEventAuthenticationMessage,
                            BKSHIDEventAuthenticationOriginator,
                            BKSHIDEventBaseAttributes,
                            BKSHIDEventBiometricDescriptor,
                            BKSHIDEventDeferringEnvironment,
                            BKSHIDEventDeferringPredicate,
                            BKSHIDEventDeferringResolution,
                            BKSHIDEventDeferringRule, BKSHIDEventDeferringTarget,
                            BKSHIDEventDeferringToken,
                            BKSHIDEventDeliveryMIGService,
                            BKSHIDEventDeliveryManager,
                            BKSHIDEventDeliveryPolicyObserver,
                            BKSHIDEventDescriptor,
                            BKSHIDEventDigitizerAttributes,
                            BKSHIDEventDigitizerPathAttributes,
                            BKSHIDEventDiscreteDispatchingPredicate,
                            BKSHIDEventDiscreteDispatchingRule,
                            BKSHIDEventDispatchingTarget, BKSHIDEventDisplay,
                            BKSHIDEventHitTestClientContext,
                            BKSHIDEventKeyCommand,
                            BKSHIDEventKeyCommandDescriptor,
                            BKSHIDEventKeyCommandsDispatchingPredicate,
                            BKSHIDEventKeyCommandsDispatchingRule,
                            BKSHIDEventKeyCommandsRegistration,
                            BKSHIDEventKeyboardDescriptor, BKSHIDEventObserver,
                            BKSHIDEventPointerAttributes,
                            BKSHIDEventRedirectAttributes, BKSHIDEventRouter,
                            BKSHIDEventRouterManager,
                            BKSHIDEventSenderDescriptor,
                            BKSHIDEventSenderSpecificDescriptor,
                            BKSHIDEventUsagePairDescriptor,
                            BKSHIDEventVendorDefinedDescriptor,
                            BKSHIDKeyboardDeviceProperties,
                            BKSHIDServiceConnection, BKSHIDTouchRoutingPolicy,
                            BKSHIDUISensorCharacteristics, BKSHIDUISensorMode,
                            BKSHIDUISensorService, BKSHitTestRegion,
                            BKSIAPDefaults, BKSInsecureDrawingAction,
                            BKSKeyboardDefaults, BKSLocalDefaults,
                            BKSLockdownDefaults, BKSMousePointerDevice,
                            BKSMousePointerDeviceObserverInfo,
                            BKSMousePointerDevicePreferences,
                            BKSMousePointerEventRoute,
                            BKSMousePointerPerDisplayInfo,
                            BKSMousePointerPreferencesObserverInfo,
                            BKSMousePointerService,
                            BKSMousePointerServiceSessionSpecification,
                            BKSMousePointerSuppressionAssertionDescriptor,
                            BKSMutableHIDEventAuthenticationMessage,
                            BKSMutableHIDEventDeferringPredicate,
                            BKSMutableHIDEventDeferringResolution,
                            BKSMutableHIDEventDeferringTarget,
                            BKSMutableHIDEventDiscreteDispatchingPredicate,
                            BKSMutableHIDEventKeyCommandsDispatchingPredicate,
                            BKSMutableHIDEventKeyCommandsRegistration,
                            BKSMutableHIDEventSenderDescriptor,
                            BKSMutableHIDKeyboardDeviceProperties,
                            BKSMutableHIDUISensorCharacteristics,
                            BKSMutableHIDUISensorMode,
                            BKSMutableTouchAuthenticationSpecification,
                            BKSPersistentConnectionDefaults, BKSRestartAction,
                            BKSSecureModeViolation, BKSSpringBoardDefaults,
                            BKSSystemAnimationFenceHandle, BKSSystemApplication,
                            BKSSystemApplicationClient,
                            BKSSystemGesturesTouchStreamPolicy, BKSSystemService,
                            BKSTouchAnnotation, BKSTouchAnnotationController,
                            BKSTouchAuthenticationSpecification,
                            BKSTouchDeliveryObservationService,
                            BKSTouchDeliveryPolicy,
                            BKSTouchDeliveryPolicyAssertion,
                            BKSTouchDeliveryUpdate, BKSTouchEventService,
                            BKSTouchStream, BKSTouchStreamPolicy,
                            BKSWatchdogServerWrapper,
                            _BKSAnimationFenceXPCClient,
                            _BKSCancelTouchesTouchDeliveryPolicy,
                            _BKSCarPlayDisplayScaleCache,
                            _BKSCombinedTouchDeliveryPolicy,
                            _BKSEventFocusChangeObserverInfo,
                            _BKSHIDEventAuthenticationKey,
                            _BKSShareTouchesTouchDeliveryPolicy ]
    objc-ivars:           [ BKSAccelerometer._accelerometerEventsRunLoop,
                            BKSAccelerometer._accelerometerEventsSource,
                            BKSAccelerometer._delegate, BKSAccelerometer._lock,
                            BKSAccelerometer._orientationCheckToken,
                            BKSAccelerometer._orientationEventsEnabled,
                            BKSAccelerometer._orientationEventsThread,
                            BKSAccelerometer._orientationNotificationsToken,
                            BKSAccelerometer._orientationPort,
                            BKSAccelerometer._passiveOrientationEvents,
                            BKSAccelerometer._updateInterval,
                            BKSAccelerometer._xThreshold,
                            BKSAccelerometer._yThreshold,
                            BKSAccelerometer._zThreshold,
                            BKSAlternateSystemApp._bundleId,
                            BKSAlternateSystemApp._connection,
                            BKSAlternateSystemApp._delegate,
                            BKSAlternateSystemApp._queue,
                            BKSAlternateSystemApp._state,
                            BKSAlternateSystemApp._stateChangeSemaphore,
                            BKSAlternateSystemApp._stateChangeWaiter,
                            BKSAnimationFenceAssertion._assertionName,
                            BKSAnimationFenceAssertion._fenceName,
                            BKSAnimationFenceAssertion._invalid,
                            BKSAnimationFenceAssertion._preFence,
                            BKSAnimationFenceAssertion._shouldTrace,
                            BKSAnimationFenceObserver._encodeCount,
                            BKSAnimationFenceObserver._fenceNameToDeathSentinelMap,
                            BKSAnimationFenceObserver._fenceNameToHandleNamesMap,
                            BKSAnimationFenceObserver._handleNameToFenceNameMap,
                            BKSAnimationFenceObserver._handleNameToTraceMap,
                            BKSAnimationFenceObserver._lastHandleName,
                            BKSAnimationFenceObserver._queue,
                            BKSAnimationFenceObserver._validDeathSentinelsTable,
                            BKSApplicationDataStore._fbsApplicationDataStore,
                            BKSBacklightFeatures._disableFeatures,
                            BKSBacklightFeatures._fixedBrightnessLevelWhileDisabled,
                            BKSBacklightFeatures._fixedBrightnessNitsWhileDisabled,
                            BKSButtonHapticsDefinition._representsHomeButton,
                            BKSButtonHapticsDefinition._settings,
                            BKSCAAnimationFenceHandle._caFence,
                            BKSContextRelativePoint._contextID,
                            BKSContextRelativePoint._point,
                            BKSDisplayInterstitialRenderOverlayDismissAction._overlayDescriptor,
                            BKSDisplayProgressIndicatorProperties._position,
                            BKSDisplayProgressIndicatorProperties._style,
                            BKSDisplayRenderOverlay._descriptor,
                            BKSDisplayRenderOverlayDescriptor._display,
                            BKSDisplayRenderOverlayDescriptor._displayUUID,
                            BKSDisplayRenderOverlayDescriptor._interfaceOrientation,
                            BKSDisplayRenderOverlayDescriptor._interstitial,
                            BKSDisplayRenderOverlayDescriptor._lockBacklight,
                            BKSDisplayRenderOverlayDescriptor._name,
                            BKSDisplayRenderOverlayDescriptor._progressIndicatorProperties,
                            BKSEventFocusDeferral._deferredProperties,
                            BKSEventFocusDeferral._priority,
                            BKSEventFocusDeferral._properties,
                            BKSEventFocusDeferralProperties._clientID,
                            BKSEventFocusDeferralProperties._contextID,
                            BKSEventFocusDeferralProperties._displayUUID,
                            BKSEventFocusDeferralProperties._pid,
                            BKSEventFocusManager._cachedFocusedDeferralProperties,
                            BKSEventFocusManager._calloutQueue,
                            BKSEventFocusManager._clientIdentifier,
                            BKSEventFocusManager._connection,
                            BKSEventFocusManager._focusClientQueue,
                            BKSEventFocusManager._focusDataLock,
                            BKSEventFocusManager._focusDataLock_adjustedFocusTargetPID,
                            BKSEventFocusManager._focusDataLock_adjustsFocusTargetPID,
                            BKSEventFocusManager._focusDataLock_assertions,
                            BKSEventFocusManager._focusDataLock_currentState,
                            BKSEventFocusManager._focusDataLock_manager,
                            BKSEventFocusManager._focusDataLock_pendingStatesByPriority,
                            BKSEventFocusManager._infoPerFocusChangeObserver,
                            BKSEventFocusManager._needsFlush,
                            BKSEventFocusManager._observer,
                            BKSEventFocusManager._observingAssertion,
                            BKSEventFocusManager._pid,
                            BKSEventFocusManager._propertyUpdateGeneration,
                            BKSEventFocusManager._queue_keyCommandRulesAssertion,
                            BKSExternalDefaults._lazy_iapDefaults,
                            BKSExternalDefaults._lazy_keyboardDefaults,
                            BKSExternalDefaults._lazy_lockdownDefaults,
                            BKSExternalDefaults._lazy_persistentConnectionDefaults,
                            BKSExternalDefaults._lazy_springBoardDefaults,
                            BKSHIDAuthenticatedKeyCommandSpecification._context,
                            BKSHIDAuthenticatedKeyCommandSpecification._keyCommand,
                            BKSHIDEventAuthenticationKey._generation,
                            BKSHIDEventAuthenticationKey._hmacContext,
                            BKSHIDEventAuthenticationKey._hmacInitialized,
                            BKSHIDEventAuthenticationKey._keyData,
                            BKSHIDEventAuthenticationKeyRetentionPolicy._keySigningTimeout,
                            BKSHIDEventAuthenticationKeyRetentionPolicy._keyVerificationTimeout,
                            BKSHIDEventAuthenticationKeyRing._authenticationKey,
                            BKSHIDEventAuthenticationKeyRing._authenticationKeyTimeout,
                            BKSHIDEventAuthenticationKeyRing._lock,
                            BKSHIDEventAuthenticationKeyRing._previousAuthenticationKey,
                            BKSHIDEventAuthenticationKeyRing._previousAuthenticationKeyTimeout,
                            BKSHIDEventAuthenticationKeyRing._retentionPolicy,
                            BKSHIDEventAuthenticationMessage._context,
                            BKSHIDEventAuthenticationMessage._eventType,
                            BKSHIDEventAuthenticationMessage._keyGeneration,
                            BKSHIDEventAuthenticationMessage._originIdentifier,
                            BKSHIDEventAuthenticationMessage._registrantEntitled,
                            BKSHIDEventAuthenticationMessage._signature,
                            BKSHIDEventAuthenticationMessage._timestamp,
                            BKSHIDEventAuthenticationMessage._versionedPID,
                            BKSHIDEventAuthenticationOriginator._key,
                            BKSHIDEventAuthenticationOriginator._keyLastAccessTime,
                            BKSHIDEventBaseAttributes._authenticationMessage,
                            BKSHIDEventBaseAttributes._display,
                            BKSHIDEventBaseAttributes._environment,
                            BKSHIDEventBaseAttributes._options,
                            BKSHIDEventBaseAttributes._source,
                            BKSHIDEventBaseAttributes._token,
                            BKSHIDEventBiometricDescriptor._biometricEventType,
                            BKSHIDEventDeferringEnvironment._identifier,
                            BKSHIDEventDeferringPredicate._display,
                            BKSHIDEventDeferringPredicate._environment,
                            BKSHIDEventDeferringPredicate._token,
                            BKSHIDEventDeferringResolution._bundleIdentifier,
                            BKSHIDEventDeferringResolution._display,
                            BKSHIDEventDeferringResolution._environment,
                            BKSHIDEventDeferringResolution._pid,
                            BKSHIDEventDeferringResolution._token,
                            BKSHIDEventDeferringResolution._versionedPID,
                            BKSHIDEventDeferringRule._predicate,
                            BKSHIDEventDeferringRule._reason,
                            BKSHIDEventDeferringRule._target,
                            BKSHIDEventDeferringTarget._pid,
                            BKSHIDEventDeferringTarget._token,
                            BKSHIDEventDeferringToken._CAContextID,
                            BKSHIDEventDeferringToken._stringIdentifier,
                            BKSHIDEventDeliveryManager._forTesting,
                            BKSHIDEventDeliveryManager._implicitFlushQueue,
                            BKSHIDEventDeliveryManager._lock,
                            BKSHIDEventDeliveryManager._lock_assertions,
                            BKSHIDEventDeliveryManager._lock_deferringRules,
                            BKSHIDEventDeliveryManager._lock_deferringSeed,
                            BKSHIDEventDeliveryManager._lock_discreteDispatchingRules,
                            BKSHIDEventDeliveryManager._lock_discreteDispatchingSeed,
                            BKSHIDEventDeliveryManager._lock_focusTargetOverride,
                            BKSHIDEventDeliveryManager._lock_implicitPreventFlushingAssertion,
                            BKSHIDEventDeliveryManager._lock_keyCommandsDispatchingRules,
                            BKSHIDEventDeliveryManager._lock_keyCommandsDispatchingSeed,
                            BKSHIDEventDeliveryManager._lock_keyCommandsRegistrationSeed,
                            BKSHIDEventDeliveryManager._lock_keyCommandsRegistrations,
                            BKSHIDEventDeliveryManager._lock_lastSentDeferringRules,
                            BKSHIDEventDeliveryManager._lock_lastSentDiscreteDispatchingRules,
                            BKSHIDEventDeliveryManager._lock_lastSentKeyCommandsDispatchingRules,
                            BKSHIDEventDeliveryManager._lock_lastSentKeyCommandsRegistrations,
                            BKSHIDEventDeliveryManager._lock_lastSentSetOfKeyCommandsRegistrations,
                            BKSHIDEventDeliveryManager._lock_needsFlush,
                            BKSHIDEventDeliveryManager._lock_preventFlushingReasons,
                            BKSHIDEventDeliveryManager._lock_preventFlushingSeed,
                            BKSHIDEventDeliveryManager._service,
                            BKSHIDEventDeliveryPolicyObserver._lock,
                            BKSHIDEventDeliveryPolicyObserver._lock_canReceiveEvents,
                            BKSHIDEventDeliveryPolicyObserver._lock_display,
                            BKSHIDEventDeliveryPolicyObserver._lock_environment,
                            BKSHIDEventDeliveryPolicyObserver._lock_observers,
                            BKSHIDEventDeliveryPolicyObserver._lock_resolutions,
                            BKSHIDEventDeliveryPolicyObserver._lock_token,
                            BKSHIDEventDeliveryPolicyObserver._observer,
                            BKSHIDEventDeliveryPolicyObserver._observingAssertion,
                            BKSHIDEventDescriptor._hidEventType,
                            BKSHIDEventDigitizerAttributes._digitizerSurfaceHeight,
                            BKSHIDEventDigitizerAttributes._digitizerSurfaceWidth,
                            BKSHIDEventDigitizerAttributes._initialTouchTimestamp,
                            BKSHIDEventDigitizerAttributes._maximumForce,
                            BKSHIDEventDigitizerAttributes._pathAttributes,
                            BKSHIDEventDigitizerAttributes._systemGestureStateChange,
                            BKSHIDEventDigitizerAttributes._systemGesturesPossible,
                            BKSHIDEventDigitizerAttributes._touchStreamIdentifier,
                            BKSHIDEventDigitizerPathAttributes._authenticationMessage,
                            BKSHIDEventDigitizerPathAttributes._hitTestLocationX,
                            BKSHIDEventDigitizerPathAttributes._hitTestLocationY,
                            BKSHIDEventDigitizerPathAttributes._locus,
                            BKSHIDEventDigitizerPathAttributes._pathIndex,
                            BKSHIDEventDigitizerPathAttributes._preciseLocationX,
                            BKSHIDEventDigitizerPathAttributes._preciseLocationY,
                            BKSHIDEventDigitizerPathAttributes._touchIdentifier,
                            BKSHIDEventDigitizerPathAttributes._userIdentifier,
                            BKSHIDEventDigitizerPathAttributes._zGradient,
                            BKSHIDEventDiscreteDispatchingPredicate._descriptors,
                            BKSHIDEventDiscreteDispatchingPredicate._senderDescriptors,
                            BKSHIDEventDiscreteDispatchingRule._predicate,
                            BKSHIDEventDiscreteDispatchingRule._target,
                            BKSHIDEventDispatchingTarget._environment,
                            BKSHIDEventDispatchingTarget._pid,
                            BKSHIDEventDisplay._builtin,
                            BKSHIDEventDisplay._hardwareIdentifier,
                            BKSHIDEventHitTestClientContext._contextID,
                            BKSHIDEventHitTestClientContext._pid,
                            BKSHIDEventKeyCommand._commandModifiedInput,
                            BKSHIDEventKeyCommand._input,
                            BKSHIDEventKeyCommand._keyCode,
                            BKSHIDEventKeyCommand._modifierFlags,
                            BKSHIDEventKeyCommand._shiftModifiedInput,
                            BKSHIDEventKeyCommand._unmodifiedInput,
                            BKSHIDEventKeyCommandsDispatchingPredicate._senderDescriptors,
                            BKSHIDEventKeyCommandsDispatchingRule._predicate,
                            BKSHIDEventKeyCommandsDispatchingRule._targets,
                            BKSHIDEventKeyCommandsRegistration._environment,
                            BKSHIDEventKeyCommandsRegistration._keyCommands,
                            BKSHIDEventKeyCommandsRegistration._token,
                            BKSHIDEventObserver._calloutQueue,
                            BKSHIDEventObserver._connection,
                            BKSHIDEventObserver._lock,
                            BKSHIDEventObserver._lock_deferringAssertionsToObservers,
                            BKSHIDEventObserver._lock_deferringResolutions,
                            BKSHIDEventPointerAttributes._acceleratedRelativePositionX,
                            BKSHIDEventPointerAttributes._acceleratedRelativePositionY,
                            BKSHIDEventPointerAttributes._activeModifiers,
                            BKSHIDEventPointerAttributes._fingerDownCount,
                            BKSHIDEventPointerAttributes._hitTestContexts,
                            BKSHIDEventPointerAttributes._pointerEdgeMask,
                            BKSHIDEventPointerAttributes._pointerState,
                            BKSHIDEventPointerAttributes._unacceleratedRelativePositionX,
                            BKSHIDEventPointerAttributes._unacceleratedRelativePositionY,
                            BKSHIDEventRedirectAttributes._pid,
                            BKSHIDEventRouter._destination,
                            BKSHIDEventRouter._queue,
                            BKSHIDEventRouter._queue_cachedHidEventDescriptors,
                            BKSHIDEventRouter._queue_delegate,
                            BKSHIDEventRouter._queue_hidEventDescriptors,
                            BKSHIDEventRouterManager._deliveryManager,
                            BKSHIDEventRouterManager._queue,
                            BKSHIDEventRouterManager._queue_dispatchingRulesAssertion,
                            BKSHIDEventRouterManager._queue_needsFlush,
                            BKSHIDEventRouterManager._queue_routers,
                            BKSHIDEventSenderDescriptor._associatedDisplay,
                            BKSHIDEventSenderDescriptor._authenticated,
                            BKSHIDEventSenderDescriptor._hardwareType,
                            BKSHIDEventSenderDescriptor._primaryPage,
                            BKSHIDEventSenderDescriptor._primaryUsage,
                            BKSHIDEventSenderDescriptor._senderID,
                            BKSHIDEventSenderSpecificDescriptor._senderID,
                            BKSHIDEventSenderSpecificDescriptor._sourceDescriptor,
                            BKSHIDEventUsagePairDescriptor._page,
                            BKSHIDEventUsagePairDescriptor._usage,
                            BKSHIDKeyboardDeviceProperties._capsLockKeyHasLanguageSwitchLabel,
                            BKSHIDKeyboardDeviceProperties._countryCode,
                            BKSHIDKeyboardDeviceProperties._language,
                            BKSHIDKeyboardDeviceProperties._layout,
                            BKSHIDKeyboardDeviceProperties._standardType,
                            BKSHIDKeyboardDeviceProperties._subinterfaceID,
                            BKSHIDTouchRoutingPolicy._settings,
                            BKSHIDUISensorCharacteristics._hasDiscreteProximitySensor,
                            BKSHIDUISensorMode._alwaysOnGesturesEnabled,
                            BKSHIDUISensorMode._changeSource,
                            BKSHIDUISensorMode._coverGestureEnabled,
                            BKSHIDUISensorMode._digitizerEnabled,
                            BKSHIDUISensorMode._estimatedProximityMode,
                            BKSHIDUISensorMode._pocketTouchesExpected,
                            BKSHIDUISensorMode._postEventWithCurrentDetectionMask,
                            BKSHIDUISensorMode._proximityDetectionMode,
                            BKSHIDUISensorMode._reason,
                            BKSHIDUISensorMode._tapToWakeEnabled,
                            BKSHIDUISensorMode._versionedPID,
                            BKSHIDUISensorService._lock,
                            BKSHIDUISensorService._modeAssertion,
                            BKSHIDUISensorService._prevailingMode,
                            BKSHitTestRegion._exclusiveTouchNormalizedSubRect,
                            BKSHitTestRegion._exclusiveTouchNormalizedSubRectInReferenceSpace,
                            BKSHitTestRegion._rect,
                            BKSLocalDefaults._disableStudyLogALSLogging,
                            BKSLocalDefaults._disableStudyLogAccelerometerLogging,
                            BKSLocalDefaults._disableStudyLogGyroLogging,
                            BKSMousePointerDevice._hasVirtualMouseButtons,
                            BKSMousePointerDevice._manufacturerName,
                            BKSMousePointerDevice._preferenceKey,
                            BKSMousePointerDevice._productName,
                            BKSMousePointerDevice._senderDescriptor,
                            BKSMousePointerDevice._supportsDragLock,
                            BKSMousePointerDeviceObserverInfo._observer,
                            BKSMousePointerDeviceObserverInfo._visibleDevices,
                            BKSMousePointerDevicePreferences._buttonConfigurationForHardwareButtonMice,
                            BKSMousePointerDevicePreferences._buttonConfigurationForVirtualButtonMice,
                            BKSMousePointerDevicePreferences._doubleTapDragMode,
                            BKSMousePointerDevicePreferences._enableNaturalScrolling,
                            BKSMousePointerDevicePreferences._enableTapToClick,
                            BKSMousePointerDevicePreferences._enableTwoFingerSecondaryClick,
                            BKSMousePointerDevicePreferences._pointerAccelerationFactor,
                            BKSMousePointerDevicePreferences._scrollAccelerationFactor,
                            BKSMousePointerEventRoute._contextID,
                            BKSMousePointerPerDisplayInfo._globalEventsAssertion,
                            BKSMousePointerPerDisplayInfo._pointerSuppressionAssertion,
                            BKSMousePointerPerDisplayInfo._previouslyRoutedContextIDs,
                            BKSMousePointerPreferencesObserverInfo._observer,
                            BKSMousePointerService._attachedDevices,
                            BKSMousePointerService._connection,
                            BKSMousePointerService._connectionQueue,
                            BKSMousePointerService._deviceConnectionObservers,
                            BKSMousePointerService._displayUUIDToPerDisplayInfo,
                            BKSMousePointerService._isObservingDeviceConnection,
                            BKSMousePointerService._isObservingPreferences,
                            BKSMousePointerService._lock,
                            BKSMousePointerService._preferencesObservers,
                            BKSMousePointerSuppressionAssertionDescriptor._suppressionOptions,
                            BKSSecureModeViolation._contextIds,
                            BKSSecureModeViolation._layerNamesByContext,
                            BKSSecureModeViolation._processId,
                            BKSSystemAnimationFenceHandle._caFence,
                            BKSSystemAnimationFenceHandle._fenceName,
                            BKSSystemAnimationFenceHandle._handleName,
                            BKSSystemAnimationFenceHandle._preFence,
                            BKSSystemAnimationFenceHandle._preFenceTrigger,
                            BKSSystemAnimationFenceHandle._shouldTrace,
                            BKSSystemAnimationFenceHandle._skipSync,
                            BKSSystemAnimationFenceHandle._valid,
                            BKSSystemApplication._client,
                            BKSSystemApplication._delegate,
                            BKSSystemApplication._queue,
                            BKSSystemApplication._systemIdleSleepInterval,
                            BKSSystemApplication._waitForDataMigration,
                            BKSSystemApplicationClient._callOutQueue,
                            BKSSystemApplicationClient._checkinSemaphore,
                            BKSSystemApplicationClient._delegate,
                            BKSSystemApplicationClient._pendingCheckIn,
                            BKSSystemApplicationClient._pingSemaphore,
                            BKSSystemApplicationClient._sentConnect,
                            BKSSystemApplicationClient._systemIdleSleepInterval,
                            BKSSystemApplicationClient._waitingForPing,
                            BKSSystemService._fbsSystemService,
                            BKSTouchAnnotation._text,
                            BKSTouchAnnotation._touchIdentifier,
                            BKSTouchAnnotation._uniqueIdentifier,
                            BKSTouchAuthenticationSpecification._authenticationMessageContext,
                            BKSTouchAuthenticationSpecification._displays,
                            BKSTouchAuthenticationSpecification._slotID,
                            BKSTouchDeliveryObservationService._calloutQueue,
                            BKSTouchDeliveryObservationService._connection,
                            BKSTouchDeliveryObservationService._generalObservers,
                            BKSTouchDeliveryObservationService._observersToTouchIdentifiers,
                            BKSTouchDeliveryObservationService._touchClientQueue,
                            BKSTouchDeliveryObservationService._touchIdentifierToObserverLists,
                            BKSTouchDeliveryPolicyAssertion._listener,
                            BKSTouchDeliveryUpdate._contextID,
                            BKSTouchDeliveryUpdate._isDetached,
                            BKSTouchDeliveryUpdate._pid,
                            BKSTouchDeliveryUpdate._touchIdentifier,
                            BKSTouchDeliveryUpdate._type,
                            BKSTouchEventService._authenticationSpecificationAssertion,
                            BKSTouchStream._reference,
                            BKSTouchStreamPolicy._shouldSendAmbiguityRecommendations,
                            _BKSCancelTouchesTouchDeliveryPolicy._assertionEndpoint,
                            _BKSCancelTouchesTouchDeliveryPolicy._contextId,
                            _BKSCancelTouchesTouchDeliveryPolicy._initialTouchTimestamp,
                            _BKSCarPlayDisplayScaleCache._displayUUIDInAppendOrder,
                            _BKSCarPlayDisplayScaleCache._displayUUIDToScale,
                            _BKSCombinedTouchDeliveryPolicy._policies,
                            _BKSEventFocusChangeObserverInfo._propertyUpdateGeneration,
                            _BKSEventFocusChangeObserverInfo._valid,
                            _BKSHIDEventAuthenticationKey._hmacContext,
                            _BKSHIDEventAuthenticationKey._hmacInitialized,
                            _BKSHIDEventAuthenticationKey._keyData,
                            _BKSShareTouchesTouchDeliveryPolicy._assertionEndpoint,
                            _BKSShareTouchesTouchDeliveryPolicy._childContextId,
                            _BKSShareTouchesTouchDeliveryPolicy._hostContextId ]
...

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>application-identifier</key>
	<string>com.LLLL.plugin</string>
	<key>com.apple.QuartzCore.displayable-context</key>
	<true/>
	<key>com.apple.QuartzCore.secure-mode</key>
	<true/>
	<key>com.apple.backboard.client</key>
	<true/>
	<key>com.apple.private.allow-background-haptics</key>
	<true/>
	<key>com.apple.private.hid.client.event-dispatch</key>
	<true/>
	<key>com.apple.private.hid.client.event-filter</key>
	<true/>
	<key>com.apple.private.hid.client.event-monitor</key>
	<true/>
	<key>com.apple.private.hid.client.service-protected</key>
	<true/>
	<key>com.apple.private.hid.manager.client</key>
	<true/>
	<key>com.apple.private.kernel.jetsam</key>
	<true/>
	<key>com.apple.private.memorystatus</key>
	<true/>
	<key>com.apple.private.persona-mgmt</key>
	<true/>
	<key>com.apple.private.security.no-sandbox</key>
	<true/>
	<key>com.apple.springboard.CFUserNotification</key>
	<true/>
	<key>com.apple.springboard.accessibility-window-hosting</key>
	<true/>
	<key>com.apple.springboard.appbackgroundstyle</key>
	<true/>
	<key>com.apple.springboard.disallowControlCenter</key>
	<true/>
	<key>com.apple.springboard.disallowNotificationCenter</key>
	<true/>
	<key>file-read-data</key>
	<true/>
	<key>platform-application</key>
	<true/>
	<key>user-preference-read</key>
	<true/>
	<key>user-preference-write</key>
	<true/>
	<key>get-task-allow</key>
	<true/>
	<key>task_for_pid-allow</key>
	<true/>
	<key>proc_info-allow</key>
	<true/>
	<key>com.apple.private.MobileGestalt.AllowedProtectedKeys</key>
	<true/>
	<key>com.apple.lsapplicationworkspace.rebuildappdatabases</key>
	<true/>
	<key>com.apple.managedconfiguration.profiled-access</key>
	<true/>
	<key>com.apple.frontboard.launchapplications</key>
	<true/>
	<key>New itemcom.apple.private.MobileContainerManager.allowed</key>
	<true/>
	<key>com.apple.coremedia.allow-protected-content-playback</key>
	<true/>
	<key>com.apple.springboard.debugapplications</key>
	<true/>
	<key>run-unsigned-code</key>
	<true/>
	<key>com.apple.springboard.launchapplications</key>
	<true/>
	<key>com.apple.coreaudio.allow-amr-decode</key>
	<true/>
	<key>com.apple.private.security.no-container</key>
	<true/>
	<key>com.apple.frontboard.shutdown</key>
	<true/>
	<key>com.apple.springboard.opensensitiveurl</key>
	<true/>
	<key>com.apple.security.iokit-user-client-class</key>
	<array>
		<string>AGXDeviceUserClient</string>
		<string>IOHDIXControllerUserClient</string>
		<string>IOSurfaceRootUserClient</string>
	</array>
	<key>keychain-access-groups</key>
	<array>
		<string>com.apple.cfnetwork</string>
		<string>com.apple.identities</string>
		<string>com.apple.mobilesafari</string>
	</array>
	<key>com.apple.CommCenter.fine-grained</key>
	<array>
		<string>spi</string>
		<string>phone</string>
		<string>identity</string>
		<string>cellular-plan</string>
		<string>data-usage</string>
		<string>data-allowed</string>
		<string>data-allowed-write</string>
		<string>preferences-write</string>
	</array>
	<key>com.apple.private.mobileinstall.allowedSPI</key>
	<array>
		<string>Lookup</string>
		<string>Install</string>
		<string>Browse</string>
		<string>Uninstall</string>
		<string>LookupForLaunchServices</string>
		<string>InstallForLaunchServices</string>
		<string>BrowseForLaunchServices</string>
		<string>UninstallForLaunchServices</string>
		<string>CopyDiskUsageForLaunchServices</string>
		<string>InstallLocalProvisioned</string>
	</array>
</dict>
</plist>

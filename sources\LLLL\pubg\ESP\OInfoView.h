

#ifndef OInfoView_h
#define OInfoView_h

#import <UIKit/UIKit.h>

@interface OInfoView : UIView
@property(nonatomic,retain)UIImageView *weapon;
@property(nonatomic,retain)UILabel *teamLb;
@property(nonatomic,retain)UILabel *nameLb;
@property(nonatomic,retain)UILabel *arrowLb;
@property(nonatomic,retain)UILabel *disLb;
@property(nonatomic,retain)UIView *infoBar;
@property(nonatomic,retain)UIView *xueBar;
@property(nonatomic,assign)CGPoint point;
@property(nonatomic,retain)CAShapeLayer *delta;

@property(nonatomic,retain)NSString *name;
@property(nonatomic)CGFloat dis;
@property(nonatomic)CGFloat xue;
@property(nonatomic)int team;
@property(nonatomic)Boolean ai;

@property(nonatomic)int WeaponID;

-(UIColor *)colorWithHex:(NSUInteger)hex
                     alpha:(CGFloat)alpha;

@end

#endif /* OInfoView_h */

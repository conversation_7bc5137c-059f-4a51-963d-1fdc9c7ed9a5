--- !tapi-tbd-v3
archs:                 [ armv7, armv7s, arm64, arm64e ]
platform:              ios
flags:                 [ flat_namespace ]
install-name:          /System/Library/PrivateFrameworks/SpringBoardServices.framework/SpringBoardServices
current-version:       1
compatibility-version: 1
objc-constraint:       retain_release
exports:
  - archs:                [ armv7, armv7s, arm64, arm64e ]
    symbols:              [ _NSStringFromAnalyticsEventType,
                            _NSStringFromAnalyticsQueryName,
                            _NSStringFromSBIdleTimerConfigSettingPrecedence,
                            _NSStringFromSBSHardwareButtonEventPriority,
                            _NSStringFromSBSHardwareButtonHapticType,
                            _NSStringFromSBSHardwareButtonKind,
                            _NSStringFromSBSHardwareButtonToggleState,
                            _NSStringFromSBSRemoteContentDismissType,
                            _SBActivateAssistantWithContext,
                            _SBAddAlertItemsSuppressionAssertion,
                            _SBAddBiometricAssertion,
                            _SBAddDownloadingIconForDisplayIdentifier,
                            _SBAddWallpaperAnimationSuspensionAssertion,
                            _SBAddWebClipToHomeScreen,
                            _SBAppSwitcherServiceRegister,
                            _SBApplicationDisplayIdentifiers,
                            _SBApplicationMostElevatedStateForProcessIDKey,
                            _SBApplicationNotificationStateChanged,
                            _SBApplicationStateBeginGeneratingChangeNotifications,
                            _SBApplicationStateDisplayIDKey,
                            _SBApplicationStateEndGeneratingChangeNotifications,
                            _SBApplicationStateGetDescription,
                            _SBApplicationStateKey,
                            _SBApplicationStateProcessIDKey,
                            _SBApplicationStateRunningReasonAssertionIdentifierKey,
                            _SBApplicationStateRunningReasonAssertionReasonKey,
                            _SBApplicationStateRunningReasonsKey,
                            _SBAuthLoggingSubsystem, _SBAutoLockTimerCategoryKey,
                            _SBAutoLockTimerFiredNotification,
                            _SBBiometricLoggingSubsystem,
                            _SBBundlePathForDisplayIdentifier,
                            _SBCardItemsControllerClientInterface,
                            _SBCardItemsControllerServerInterface,
                            _SBClearWallpaperAsset,
                            _SBDashBoardChargingAssertionIdentifier,
                            _SBDashBoardNowPlayingAssertionIdentifier,
                            _SBDashBoardSleepAssertionIdentifier, _SBDataReset,
                            _SBDeactivateReachability, _SBDimScreen,
                            _SBDismissSheetView, _SBDisplayIdentifierForPID,
                            _SBDisplayIdentifiersForPID,
                            _SBDisplayLayoutBacklightTransitionReasonACPowerChange,
                            _SBDisplayLayoutBacklightTransitionReasonAlert,
                            _SBDisplayLayoutBacklightTransitionReasonBoot,
                            _SBDisplayLayoutBacklightTransitionReasonCameraButton,
                            _SBDisplayLayoutBacklightTransitionReasonCar,
                            _SBDisplayLayoutBacklightTransitionReasonChargingAccessoryChange,
                            _SBDisplayLayoutBacklightTransitionReasonCoverSheet,
                            _SBDisplayLayoutBacklightTransitionReasonExternalAppRequest,
                            _SBDisplayLayoutBacklightTransitionReasonExternalRequest,
                            _SBDisplayLayoutBacklightTransitionReasonHomeButton,
                            _SBDisplayLayoutBacklightTransitionReasonIdleTimer,
                            _SBDisplayLayoutBacklightTransitionReasonKeyboard,
                            _SBDisplayLayoutBacklightTransitionReasonLanguageChange,
                            _SBDisplayLayoutBacklightTransitionReasonLiftToWake,
                            _SBDisplayLayoutBacklightTransitionReasonLockButton,
                            _SBDisplayLayoutBacklightTransitionReasonLogout,
                            _SBDisplayLayoutBacklightTransitionReasonMouseButton,
                            _SBDisplayLayoutBacklightTransitionReasonNotification,
                            _SBDisplayLayoutBacklightTransitionReasonNotificationCenter,
                            _SBDisplayLayoutBacklightTransitionReasonOtherButton,
                            _SBDisplayLayoutBacklightTransitionReasonPlugin,
                            _SBDisplayLayoutBacklightTransitionReasonPocketState,
                            _SBDisplayLayoutBacklightTransitionReasonPowerDownDismiss,
                            _SBDisplayLayoutBacklightTransitionReasonPrefix,
                            _SBDisplayLayoutBacklightTransitionReasonProgrammatic,
                            _SBDisplayLayoutBacklightTransitionReasonProx,
                            _SBDisplayLayoutBacklightTransitionReasonRestoring,
                            _SBDisplayLayoutBacklightTransitionReasonSOSDismiss,
                            _SBDisplayLayoutBacklightTransitionReasonSiri,
                            _SBDisplayLayoutBacklightTransitionReasonSmartCover,
                            _SBDisplayLayoutBacklightTransitionReasonSpringBoardRequest,
                            _SBDisplayLayoutBacklightTransitionReasonTouch,
                            _SBDisplayLayoutBacklightTransitionReasonUnknownUserEvent,
                            _SBDisplayLayoutBacklightTransitionReasonVolumeButton,
                            _SBDisplayLayoutTransitionReasonAccessibility,
                            _SBDisplayLayoutTransitionReasonAppLibraryCategory,
                            _SBDisplayLayoutTransitionReasonAppLibraryCategoryExpanded,
                            _SBDisplayLayoutTransitionReasonAppLibraryCategoryRecents,
                            _SBDisplayLayoutTransitionReasonAppLibraryCategorySuggested,
                            _SBDisplayLayoutTransitionReasonAppLibrarySearch,
                            _SBDisplayLayoutTransitionReasonAppSwitcher,
                            _SBDisplayLayoutTransitionReasonBannerUnfurl,
                            _SBDisplayLayoutTransitionReasonBreadcrumb,
                            _SBDisplayLayoutTransitionReasonCommandTab,
                            _SBDisplayLayoutTransitionReasonControlCenter,
                            _SBDisplayLayoutTransitionReasonDestroySceneAction,
                            _SBDisplayLayoutTransitionReasonExternalRequest,
                            _SBDisplayLayoutTransitionReasonFloatingApplication,
                            _SBDisplayLayoutTransitionReasonFloatingDock,
                            _SBDisplayLayoutTransitionReasonFloatingDockAppSuggestion,
                            _SBDisplayLayoutTransitionReasonFloatingDockRecents,
                            _SBDisplayLayoutTransitionReasonHomeScreen,
                            _SBDisplayLayoutTransitionReasonInlineAppExpose,
                            _SBDisplayLayoutTransitionReasonLockScreen,
                            _SBDisplayLayoutTransitionReasonNotification,
                            _SBDisplayLayoutTransitionReasonNotificationCenter,
                            _SBDisplayLayoutTransitionReasonPrefix,
                            _SBDisplayLayoutTransitionReasonRequestSceneAction,
                            _SBDisplayLayoutTransitionReasonSideAppSwitcher,
                            _SBDisplayLayoutTransitionReasonSiri,
                            _SBDisplayLayoutTransitionReasonSpotlight,
                            _SBDisplayLayoutTransitionReasonSystemGesture,
                            _SBDisplayLayoutTransitionReasonWidget,
                            _SBDisplayMetrics, _SBEnableLockScreenBundle,
                            _SBFlashColor,
                            _SBFrontmostApplicationDisplayIdentifier,
                            _SBGetApplicationNetworkFlags,
                            _SBGetBatteryAwakeTime,
                            _SBGetBatteryUsageTimesInSeconds,
                            _SBGetCurrentBacklightFactor,
                            _SBGetDisplayIdentifiers,
                            _SBGetDisplayIdentifiersForExternalAccessoryProtocols,
                            _SBGetFlattenedIconState,
                            _SBGetHomeScreenIconMetrics, _SBGetIconPNGData,
                            _SBGetIconState, _SBGetInterfaceOrientation,
                            _SBGetIsAlive, _SBGetMediaVolume,
                            _SBGetNowPlayingAppBundleIdentifier,
                            _SBGetPendingIconState, _SBGetRecentSleepsWakes,
                            _SBGetScheduledPowerEvents, _SBGetScreenLockStatus,
                            _SBGetWallpaperLegibilitySettings,
                            _SBGetWallpaperOptions, _SBGetWallpaperPreview,
                            _SBGetWallpaperPreviewSurface,
                            _SBInterruptKeybagRefetch, _SBIsReachabilityEnabled,
                            _SBIsSystemApplication,
                            _SBLocalizedApplicationNameForDisplayIdentifier,
                            _SBLockDevice, _SBLockDeviceAndFeatures,
                            _SBLogAccessibilityWindowHosting, _SBLogAnalytics,
                            _SBLogAppQuitMonitor, _SBLogAppShortcuts,
                            _SBLogAuthenticationAssertions,
                            _SBLogAuthenticationController,
                            _SBLogAuthenticationKeybag,
                            _SBLogAuthenticationModel,
                            _SBLogAuthenticationRequests, _SBLogAutoLaunching,
                            _SBLogBanners, _SBLogBiometricResource,
                            _SBLogCFUserNotifications, _SBLogChargingCenter,
                            _SBLogCommon, _SBLogCoverSheet, _SBLogDashBoard,
                            _SBLogDashBoardCallToActionLabel,
                            _SBLogDashBoardHostedAppViewController,
                            _SBLogDashBoardScrollGestures,
                            _SBLogDashBoardTelemetrySignposts,
                            _SBLogDoNotDisturbBedtime,
                            _SBLogDoNotDisturbWhileDriving, _SBLogDockRecents,
                            _SBLogIconDragging, _SBLogIdleTimer,
                            _SBLogInCallPresentation, _SBLogLiquidDetection,
                            _SBLogLockScreenBiometricCoordinator,
                            _SBLogLockScreenBiometricFaceIDCoaching,
                            _SBLogLockScreenBiometricWalletPreArm,
                            _SBLogLockScreenMesaHomeButtonPasscodeRecognizer,
                            _SBLogLockScreenMesaHomeButtonSuppressAfterUnlockRecognizer,
                            _SBLogLockScreenMesaUnlockBehaviors,
                            _SBLogLockScreenMesaWalletPreArm,
                            _SBLogLockScreenNowPlaying,
                            _SBLogMedusaDropDestination, _SBLogMotionAlarm,
                            _SBLogMotionGesture, _SBLogPhoneUnlockWithWatch,
                            _SBLogPiP, _SBLogRootController, _SBLogScreenWake,
                            _SBLogStatusBarish, _SBLogTransientOverlay,
                            _SBLogWallet, _SBLogWallpaper, _SBLogWebClip,
                            _SBLogWidgets, _SBLoggingSubsystem,
                            _SBMigGetFunctionForSymbol, _SBOverrideDisplayedDate,
                            _SBPresentPowerDownUI, _SBPresentSheetView,
                            _SBProgrammaticSwitchAppGestureMoveToLeft,
                            _SBProgrammaticSwitchAppGestureMoveToRight,
                            _SBReboot, _SBRegisterRemoteView,
                            _SBReloadIconForIdentifier,
                            _SBRemoveWebClipFromHomeScreen,
                            _SBSAccessoryUIDismissDuration,
                            _SBSAcquireBiometricUnlockSuppressionAssertion,
                            _SBSActivateAssistant,
                            _SBSActivateAssistantWithContext,
                            _SBSAlertItemsSuppressionAssertionCreate,
                            _SBSAlertItemsSuppressionAssertionGetTypeID,
                            _SBSAppDragPrivateTypeIdentifier,
                            _SBSAppLaunchOriginSpotlight,
                            _SBSAppSwitcherQuitAppNotification,
                            _SBSAppSwitcherServiceMessageKeyBundleIdentifier,
                            _SBSAppSwitcherServiceMessageKeyPort,
                            _SBSAppSwitcherServiceMessageKeySuccessResult,
                            _SBSAppSwitcherServiceRegister,
                            _SBSAppSwitcherServiceRegistrationGetBundleID,
                            _SBSAppSwitcherServiceRegistrationGetIsValid,
                            _SBSAppSwitcherServiceRegistrationGetTypeID,
                            _SBSAppSwitcherServiceRegistrationGetViewControllerClassName,
                            _SBSApplicationCanBeLaunched,
                            _SBSApplicationLaunchFromURLOptionUnlockDeviceKey,
                            _SBSApplicationLaunchOptionAppLinkOpenStrategyChangedKey,
                            _SBSApplicationLaunchOptionBreadcrumbBundleIdKey,
                            _SBSApplicationLaunchOptionBrowserActivationWithNoURLKey,
                            _SBSApplicationLaunchOptionBrowserReuseTabKey,
                            _SBSApplicationLaunchOptionLaunchInClassicModeKey,
                            _SBSApplicationLaunchOptionLaunchSuspendedKey,
                            _SBSApplicationLaunchOptionPromptUnlockKey,
                            _SBSApplicationLaunchOptionRevealIconKey,
                            _SBSApplicationLaunchOptionUnlockDeviceKey,
                            _SBSApplicationLaunchOptionUpdateAppLinkOpenStrategyKey,
                            _SBSApplicationLaunchingErrorString,
                            _SBSApplicationMultiwindowServiceTriggerShowAllWindowsEntitlement,
                            _SBSApplicationServiceIdentifier,
                            _SBSApplicationShortcutCustomImageIconDataTypeKey,
                            _SBSApplicationShortcutCustomImageIconImageDataKey,
                            _SBSApplicationShortcutCustomImageIconIsTemplateKey,
                            _SBSApplicationShortcutItemActivationModeForString,
                            _SBSApplicationShortcutServiceCustomImageEntitlement,
                            _SBSApplicationShortcutServiceFullAccessEntitlement,
                            _SBSApplicationShortcutSystemIconTypeForString,
                            _SBSApplicationTerminationAssertionCreate,
                            _SBSApplicationTerminationAssertionCreateWithError,
                            _SBSApplicationTerminationAssertionErrorString,
                            _SBSApplicationTerminationAssertionGetDisplayIdentifier,
                            _SBSApplicationTerminationAssertionGetIsValid,
                            _SBSApplicationTerminationAssertionGetTypeID,
                            _SBSApplicationTerminationAssertionInvalidate,
                            _SBSAssistantActivationContextBundleIDKey,
                            _SBSBacklightChangeSourceForDisplayLayoutTransitionReason,
                            _SBSBatteryUIDismissDuration,
                            _SBSBiometricsServiceUnlockSuppressionAssertionEntitlement,
                            _SBSCleanupClientEntitlementEnforcementPort,
                            _SBSConvertOpenApplicationSBSKeysToFBSKeysIfNecessary,
                            _SBSCopyApplicationDisplayIdentifiers,
                            _SBSCopyBundleInfoValueForKeyAndProcessID,
                            _SBSCopyBundlePathForDisplayIdentifier,
                            _SBSCopyDisplayIdentifierForProcessID,
                            _SBSCopyDisplayIdentifiers,
                            _SBSCopyDisplayIdentifiersForExternalAccessoryProtocol,
                            _SBSCopyDisplayIdentifiersForProcessID,
                            _SBSCopyExecutablePathForDisplayIdentifier,
                            _SBSCopyFrontmostApplicationDisplayIdentifier,
                            _SBSCopyIconImagePNGDataForDisplayIdentifier,
                            _SBSCopyIconImagePathForDisplayIdentifier,
                            _SBSCopyInfoForApplicationWithProcessID,
                            _SBSCopyLocalizedApplicationNameForDisplayIdentifier,
                            _SBSCopyNowPlayingAppBundleIdentifier,
                            _SBSCreateClientEntitlementEnforcementPort,
                            _SBSCreateExternalDisplayLayoutServiceEndpoint,
                            _SBSCreateOpenApplicationService,
                            _SBSCreateOpenApplicationServiceEndpoint,
                            _SBSDisplayLayoutBacklightTransitionReasonForBacklightChangeSource,
                            _SBSDisplayLayoutElementAppLibraryIdentifier,
                            _SBSDisplayLayoutElementAppLibraryOpenPodIdentifier,
                            _SBSDisplayLayoutElementAppLibraryWidgetEditingIdentifier,
                            _SBSDisplayLayoutElementAppSwitcherIdentifier,
                            _SBSDisplayLayoutElementCarPlayOEMIdentifier,
                            _SBSDisplayLayoutElementCarTemplateMapsIdentifier,
                            _SBSDisplayLayoutElementFloatingDockIdentifier,
                            _SBSDisplayLayoutElementHomeScreenIdentifier,
                            _SBSDisplayLayoutElementHomeScreenOpenFolderIdentifier,
                            _SBSDisplayLayoutElementLockScreenNavigationIdentifier,
                            _SBSDisplayLayoutElementLoginIdentifier,
                            _SBSDisplayLayoutElementNowPlayingIdentifier,
                            _SBSDisplayLayoutElementPasscodeIdentifier,
                            _SBSDisplayLayoutElementSideSwitcherIdentifier,
                            _SBSDisplayLayoutElementSpotlightFullscreenOverlayIdentifier,
                            _SBSDisplayLayoutElementSpotlightIdentifier,
                            _SBSDisplayLayoutElementTodayViewIdentifier,
                            _SBSDisplayLayoutRoleDescription,
                            _SBSDisplayLayoutRoleIsDefined,
                            _SBSDisplayLayoutTransitionReasonForSourceApplication,
                            _SBSDisplayLayoutTransitionSourceApplicationFromReason,
                            _SBSEventObserverEventContinuityUIBecameVisible,
                            _SBSEventObserverEventContinuityUIWasObscured,
                            _SBSEventObserverEventDimmed,
                            _SBSEventObserverEventRemoteAlertActivated,
                            _SBSEventObserverEventRemoteAlertDeactivated,
                            _SBSEventObserverEventSignificantTimeChange,
                            _SBSEventObserverEventUndimmed,
                            _SBSEventObserverEventUnlocked,
                            _SBSEventObserverGetValueForState,
                            _SBSEventObserverInitialize,
                            _SBSEventObserverStartObservingEvent,
                            _SBSEventObserverStateContinuityUIIsVisible,
                            _SBSEventObserverStateDimmedForLock,
                            _SBSEventObserverStopObservingAllEvents,
                            _SBSEventObserverStopObservingEvent,
                            _SBSExternalDisplayLayoutServiceInstanceIdentifier,
                            _SBSGetApplicationState,
                            _SBSGetMostElevatedApplicationStateForProcessID,
                            _SBSGetScreenLockStatus, _SBSGetSideSwitchPreference,
                            _SBSHardwareButtonServiceBackgroundEventConsumerEntitlement,
                            _SBSHardwareButtonServiceEventConsumerEntitlement,
                            _SBSHardwareButtonServiceHomeHardwareButtonHintSuppressionEntitlement,
                            _SBSIdentifierForSecureAppType,
                            _SBSInCallPresentationModeDescription,
                            _SBSInterruptKeybagRefetch,
                            _SBSIsReachabilityEnabled, _SBSIsSystemApplication,
                            _SBSLaunchApplicationForDebugging,
                            _SBSLaunchApplicationForDebuggingWithOptions,
                            _SBSLaunchApplicationWithIdentifier,
                            _SBSLaunchApplicationWithIdentifierAndLaunchOptions,
                            _SBSLaunchApplicationWithIdentifierAndURL,
                            _SBSLaunchApplicationWithIdentifierAndURLAndLaunchOptions,
                            _SBSLockDevice,
                            _SBSLockScreenContentActionConfigurationKeySupportedOrientations,
                            _SBSLockScreenContentAssertionErrorDomain,
                            _SBSLockScreenContentAssertionSlotApp,
                            _SBSLockScreenContentAssertionSlotCharm,
                            _SBSLockScreenContentAssertionSlotEmbedded,
                            _SBSLockScreenContentAssertionSlotPlatter,
                            _SBSLockScreenContentAssertionSlotRemoteAlert,
                            _SBSLockScreenServiceMessageKeySuccessResult,
                            _SBSLockScreenServiceMessageKeyUnlockOptions,
                            _SBSMinimalVolumeControlHUDIdentifier,
                            _SBSOpenApplicationDebugOptionKeyWidgetFamily,
                            _SBSOpenApplicationDebugOptionKeyWidgetKind,
                            _SBSOpenApplicationDebugOptionKeyWidgetSize,
                            _SBSOpenApplicationEnvironmentSecureOnLockScreen,
                            _SBSOpenApplicationLaunchOriginShortcutItem,
                            _SBSOpenApplicationLayoutRolePrimary,
                            _SBSOpenApplicationLayoutRoleSideLarge,
                            _SBSOpenApplicationLayoutRoleSideNarrow,
                            _SBSOpenApplicationOptionKeyAdditionalApplications,
                            _SBSOpenApplicationOptionKeyConfirmedNotInPocket,
                            _SBSOpenApplicationOptionKeyDisableWatchdog,
                            _SBSOpenApplicationOptionKeyLaunchApplicationAfterSetup,
                            _SBSOpenApplicationOptionKeyLaunchEnvironment,
                            _SBSOpenApplicationOptionKeyLaunchSceneless,
                            _SBSOpenApplicationOptionKeyLaunchWithoutAnimation,
                            _SBSOpenApplicationOptionKeyLayoutRole,
                            _SBSOpenDataActivationURL, _SBSOpenSensitiveURL,
                            _SBSOpenSensitiveURLAndUnlock,
                            _SBSOpenURLOptionKeyUseLiveContentDuringTransition,
                            _SBSOverrideDisplayedDate, _SBSPresentPowerDownUI,
                            _SBSProcessAssertionCopyIdentifier,
                            _SBSProcessAssertionCreateForPID,
                            _SBSProcessAssertionGetNameForReason,
                            _SBSProcessAssertionGetTypeID,
                            _SBSProcessAssertionIsValid,
                            _SBSProcessAssertionSetFlags,
                            _SBSProcessAssertionSetInvalidationCallBack,
                            _SBSProcessIDForDisplayIdentifier,
                            _SBSRegisterDisplayIdentifiersChangedBlock,
                            _SBSRemoteAlertActivationReasonWakeTo,
                            _SBSRemoteAlertHandleInvalidationErrorCodeForInvalidationReason,
                            _SBSRemoteAlertHandleInvalidationErrorDescription,
                            _SBSRemoteAlertHandleInvalidationErrorDomain,
                            _SBSRemoteContentPreferencesBackgroundStyleKey,
                            _SBSRemoteContentPreferencesDateTimeStyleKey,
                            _SBSRemoteContentPreferencesHomeGestureModeKey,
                            _SBSRemoteContentPreferencesReducesWhitePointKey,
                            _SBSRemoteContentPreferencesSuppressesBottomEdgeContentKey,
                            _SBSRemoteContentPreferencesSuppressesNotificationsKey,
                            _SBSRequestAppSwitcherAppearanceForHiddenApplication,
                            _SBSRequestPasscodeUnlockAlertUI,
                            _SBSRequestPasscodeUnlockUI,
                            _SBSRequestSuggestedApplication,
                            _SBSRequiredContextIdsForMedusaDragAndDropForSpotlightOnly,
                            _SBSResetHomeScreenLayout,
                            _SBSSecureAppAssertionErrorDomain,
                            _SBSSecureAppTypeForIdentifier, _SBSServerPortHelper,
                            _SBSSetAlertSuppressionContexts,
                            _SBSSetAlertSuppressionContextsBySectionIdentifier,
                            _SBSSetAllApplicationsShowProgress,
                            _SBSSetAssistantRecognitionStrings, _SBSSetIdleText,
                            _SBSSetInterceptsMenuButton,
                            _SBSSetInterceptsMenuButtonForever,
                            _SBSSetReachabilityEnabled,
                            _SBSSetRequiredContextIdsForMedusaDragAndDropForSpotlightOnly,
                            _SBSSetSideSwitchPreference,
                            _SBSSetStatusBarShowsActivity,
                            _SBSSetStatusBarShowsActivityForApplication,
                            _SBSSetStatusBarShowsOverridesForRecording,
                            _SBSSetStatusBarShowsSyncActivity,
                            _SBSSetTypingActive,
                            _SBSSetVoiceRecognitionAudioInputPaths, _SBSShutDown,
                            _SBSSoftwareUpdateServicePasscodePolicyEntitlement,
                            _SBSSpringBoardBackgroundServerPort,
                            _SBSSpringBoardBlockableServerPort,
                            _SBSSpringBoardServerPort,
                            _SBSStatusBarStyleOverridesAssertionClientInterface,
                            _SBSStatusBarStyleOverridesCoordinatorErrorDomain,
                            _SBSStatusBarStyleOverridesCoordinatorErrorFailedEntitlementCheckDescription,
                            _SBSStatusBarStyleOverridesCoordinatorErrorFailedExistingCoordinatorDescription,
                            _SBSStatusBarStyleOverridesCoordinatorErrorProcessAlreadyRegisteredCoordinatorDescription,
                            _SBSStatusBarStyleOverridesCoordinatorErrorStyleOverrideKey,
                            _SBSStringFromWebClipServiceLaunchOrigin,
                            _SBSSuspendFrontmostApplication,
                            _SBSSystemServiceIdentifier,
                            _SBSTagTouchForTypingMenu,
                            _SBSThermalWarningAssertionCreateForBundleID,
                            _SBSUIActivateRemoteAlert,
                            _SBSUIActivateRemoteAlertWithLifecycleNotifications,
                            _SBSUIAppDeactivateReachability,
                            _SBSUIAppSetWantsLockButtonEvents,
                            _SBSUIAppSetWantsVolumeButtonEvents,
                            _SBSUIIsNamedRemoteAlertCurrentlyActive,
                            _SBSUIRemoteAlertOptionActivateForAssistant,
                            _SBSUIRemoteAlertOptionActivityContinuationIdentifier,
                            _SBSUIRemoteAlertOptionAllowCFUserNotificationsOnTop,
                            _SBSUIRemoteAlertOptionCarDisplay,
                            _SBSUIRemoteAlertOptionCustomActivationReason,
                            _SBSUIRemoteAlertOptionDisableFadeInAnimation,
                            _SBSUIRemoteAlertOptionDismissWithHomeButton,
                            _SBSUIRemoteAlertOptionDismissalAnimationStyle,
                            _SBSUIRemoteAlertOptionHasTranslucentBackground,
                            _SBSUIRemoteAlertOptionImpersonatesApplicationBundleID,
                            _SBSUIRemoteAlertOptionInitialBackgroundStyle,
                            _SBSUIRemoteAlertOptionLaunchingInterfaceOrientation,
                            _SBSUIRemoteAlertOptionStatusBarStyle,
                            _SBSUIRemoteAlertOptionSuppressSiri,
                            _SBSUIRemoteAlertOptionSwipeDismissalStyle,
                            _SBSUIRemoteAlertOptionUserInfo,
                            _SBSUIRemoteAlertOptionViewControllerClass,
                            _SBSUIRemoteAlertOptionWantsWallpaperTunnel,
                            _SBSUndimScreen,
                            _SBSUserNotificationButtonDefinitionResponseIndexKey,
                            _SBSUserNotificationButtonDefinitionsKey,
                            _SBSUserNotificationTextFieldDefinitionsKey,
                            _SBSWallpaperFetchServiceEntitlement,
                            _SBSWallpaperModificationServiceEntitlement,
                            _SBSWallpaperServiceIdentifier,
                            _SBSWatchdogAssertionCancel,
                            _SBSWatchdogAssertionCreateForPID,
                            _SBSWatchdogAssertionGetRenewalInterval,
                            _SBSWatchdogAssertionGetTypeID,
                            _SBSWatchdogAssertionRenew,
                            _SBSWirelessBatteryUIDismissDuration,
                            _SBScreenTimeCategoryKey,
                            _SBScreenTimeTrackingChangedNotification,
                            _SBScrollToIconWithDisplayIdentifier,
                            _SBSetAlertSuppressionContexts,
                            _SBSetAlertSuppressionContextsBySectionIdentifier,
                            _SBSetAllApplicationsShowProgress,
                            _SBSetAllApplicationsShowSyncIndicator,
                            _SBSetAppIsConnectedToEA,
                            _SBSetApplicationBadgeNumber,
                            _SBSetApplicationBadgeString,
                            _SBSetApplicationNetworkFlags,
                            _SBSetApplicationShowsProgress,
                            _SBSetAssistantRecognitionStrings, _SBSetIconState,
                            _SBSetIdleText, _SBSetInterceptsMenuButton,
                            _SBSetMediaVolume, _SBSetProceduralWallpaper,
                            _SBSetReachabilityEnabled,
                            _SBSetShowsOverridesForRecording,
                            _SBSetShowsProgress, _SBSetTypingActive,
                            _SBSetVoiceControlEnabled,
                            _SBSetVoiceRecognitionAudioInputPaths,
                            _SBSetWallpaperImageForLocations,
                            _SBSetWallpaperImageSurfaceForLocations,
                            _SBSetWallpaperOptionsForLocations,
                            _SBSetWallpaperVariant, _SBSetWantsLockButtonEvents,
                            _SBSetWantsVolumeButtonEvents,
                            _SBShowNetworkPromptsIfNecessary, _SBShutDown,
                            _SBStatusBarStyleOverridesAssertionServerInterface,
                            _SBSuspendFrontmostApp, _SBTagTouchForTypingMenu,
                            _SBUnregisterRemoteView,
                            _SBUserNotificationAlertMessageDelimiterKey,
                            _SBUserNotificationAllowInCarKey,
                            _SBUserNotificationAllowInLoginWindow,
                            _SBUserNotificationAllowInSetupKey,
                            _SBUserNotificationAllowInStarkKey,
                            _SBUserNotificationAllowLockscreenDismissalKey,
                            _SBUserNotificationAllowMenuButtonDismissal,
                            _SBUserNotificationAllowedApplicationsKey,
                            _SBUserNotificationAlternateButtonPresentationStyleKey,
                            _SBUserNotificationAttachmentImageAssetCatalogImageKey,
                            _SBUserNotificationAttachmentImageAssetCatalogPathKey,
                            _SBUserNotificationAttachmentImagePath,
                            _SBUserNotificationBehavesSuperModally,
                            _SBUserNotificationButtonLayoutDirectionKey,
                            _SBUserNotificationButtonTagForUnlockActionKey,
                            _SBUserNotificationDefaultButtonPresentationStyleKey,
                            _SBUserNotificationDefaultButtonTag,
                            _SBUserNotificationDefaultResponseLaunchBundleID,
                            _SBUserNotificationDefaultResponseLaunchURL,
                            _SBUserNotificationDisableIdleSleepWhileVisible,
                            _SBUserNotificationDismissOnLock,
                            _SBUserNotificationDismissesOverlaysInLockScreen,
                            _SBUserNotificationDisplayActionButtonOnLockScreen,
                            _SBUserNotificationDontDismissOnUnlock,
                            _SBUserNotificationExtensionIdentifierKey,
                            _SBUserNotificationExtensionItemsKey,
                            _SBUserNotificationForcesModalAlertAppearance,
                            _SBUserNotificationGroupsTextFields,
                            _SBUserNotificationHeaderImageAssetCatalogImageKey,
                            _SBUserNotificationHeaderImageAssetCatalogPathKey,
                            _SBUserNotificationHeaderImagePath,
                            _SBUserNotificationHideButtonsInAwayView,
                            _SBUserNotificationHideOnClonedDisplay,
                            _SBUserNotificationIconImageAssetCatalogImageKey,
                            _SBUserNotificationIconImageAssetCatalogPathKey,
                            _SBUserNotificationIconImagePath,
                            _SBUserNotificationIgnoresQuietMode,
                            _SBUserNotificationLockScreenAlertHeaderKey,
                            _SBUserNotificationLockScreenAlertMessageDelimiterKey,
                            _SBUserNotificationLockScreenAlertMessageKey,
                            _SBUserNotificationOneButtonPerLine,
                            _SBUserNotificationOtherButtonPresentationStyleKey,
                            _SBUserNotificationPendInSetupIfNotAllowedKey,
                            _SBUserNotificationPendWhileKeyBagLockedKey,
                            _SBUserNotificationRemoteServiceBundleIdentifierKey,
                            _SBUserNotificationRemoteViewControllerClassNameKey,
                            _SBUserNotificationSoundAlertTopicKey,
                            _SBUserNotificationSoundAlertTypeKey,
                            _SBUserNotificationSoundRepeatDurationKey,
                            _SBUserNotificationSoundVibrationPatternKey,
                            _SBUserNotificationSystemSoundBehaviorKey,
                            _SBUserNotificationSystemSoundIDKey,
                            _SBUserNotificationTextAutocapitalizationType,
                            _SBUserNotificationTextAutocorrectionType,
                            _SBUserNotificationUndimsScreen,
                            _SBUserNotificationUsesUndoStyle,
                            _SBUserNotificationWakeDisplay,
                            _SpringBoardUser_server,
                            _SpringBoardUser_server_routine,
                            _SpringBoardUtility_server,
                            _SpringBoardUtility_server_routine,
                            __SBApplicationStateGetMonitor,
                            __SBFScreenTimeNameForCategory,
                            __SBFScreenTimePostExternalChangeNotification,
                            __SBFScreenTimeRegisterForExternalChangeNotification,
                            __SBMigActivateAssistantWithContext,
                            __SBMigAddAlertItemsSuppressionAssertion,
                            __SBMigAddBiometricAssertion,
                            __SBMigAddDownloadingIconForDisplayIdentifier,
                            __SBMigAddWallpaperAnimationSuspensionAssertion,
                            __SBMigAddWebClipToHomeScreen,
                            __SBMigAppSwitcherServiceRegister,
                            __SBMigApplicationDisplayIdentifiers,
                            __SBMigBundlePathForDisplayIdentifier,
                            __SBMigClearWallpaperAsset,
                            __SBMigDeactivateReachability, __SBMigDimScreen,
                            __SBMigDisplayIdentifierForPID,
                            __SBMigDisplayIdentifiersForPID,
                            __SBMigEnableLockScreenBundle, __SBMigFlashColor,
                            __SBMigFrontmostApplicationDisplayIdentifier,
                            __SBMigGetActiveInterfaceOrientation,
                            __SBMigGetApplicationNetworkFlags,
                            __SBMigGetBatteryAwakeTime,
                            __SBMigGetBatteryUsageTimesInSeconds,
                            __SBMigGetCurrentBacklightFactor,
                            __SBMigGetDisplayIdentifiers,
                            __SBMigGetDisplayIdentifiersForExternalAccessoryProtocols,
                            __SBMigGetFlattenedIconState,
                            __SBMigGetHomeScreenIconMetrics,
                            __SBMigGetIconPNGData, __SBMigGetIconState,
                            __SBMigGetIsAlive, __SBMigGetMediaVolume,
                            __SBMigGetNowPlayingAppBundleIdentifier,
                            __SBMigGetPendingIconState,
                            __SBMigGetRecentSleepsWakes,
                            __SBMigGetScheduledPowerEvents,
                            __SBMigGetScreenLockStatus,
                            __SBMigGetWallpaperLegibilitySettings,
                            __SBMigGetWallpaperOptions,
                            __SBMigGetWallpaperPreview,
                            __SBMigGetWallpaperPreviewSurface,
                            __SBMigInterruptKeybagRefetch,
                            __SBMigIsReachabilityEnabled,
                            __SBMigIsSystemApplication,
                            __SBMigLocalizedApplicationNameForDisplayIdentifier,
                            __SBMigLockDevice, __SBMigLockDeviceAndFeatures,
                            __SBMigOverrideDisplayedDate,
                            __SBMigPresentPowerDownUI,
                            __SBMigProgrammaticSwitchAppGestureMoveToLeft,
                            __SBMigProgrammaticSwitchAppGestureMoveToRight,
                            __SBMigReboot, __SBMigReloadIconForIdentifier,
                            __SBMigRemoveWebClipFromHomeScreen,
                            __SBMigScrollToIconWithDisplayIdentifier,
                            __SBMigSetAlertSuppressionContexts,
                            __SBMigSetAlertSuppressionContextsBySectionIdentifier,
                            __SBMigSetAllApplicationsShowProgress,
                            __SBMigSetAllApplicationsShowSyncIndicator,
                            __SBMigSetAppIsConnectedToEA,
                            __SBMigSetApplicationBadgeNumber,
                            __SBMigSetApplicationBadgeString,
                            __SBMigSetApplicationNetworkFlags,
                            __SBMigSetApplicationShowsProgress,
                            __SBMigSetAssistantRecognitionStrings,
                            __SBMigSetIconState, __SBMigSetIdleText,
                            __SBMigSetInterceptsMenuButton,
                            __SBMigSetMediaVolume, __SBMigSetProceduralWallpaper,
                            __SBMigSetReachabilityEnabled,
                            __SBMigSetShowsOverridesForRecording,
                            __SBMigSetShowsProgress, __SBMigSetTypingActive,
                            __SBMigSetVoiceControlEnabled,
                            __SBMigSetVoiceRecognitionAudioInputPaths,
                            __SBMigSetWallpaperImageForLocations,
                            __SBMigSetWallpaperImageSurfaceForLocations,
                            __SBMigSetWallpaperOptionsForLocations,
                            __SBMigSetWallpaperVariant,
                            __SBMigSetWantsLockButtonEvents,
                            __SBMigSetWantsVolumeButtonEvents,
                            __SBMigShowNetworkPromptsIfNecessary,
                            __SBMigShutDown, __SBMigSpringBoardUser_subsystem,
                            __SBMigSpringBoardUtility_subsystem,
                            __SBMigSuspendFrontmostApp,
                            __SBMigTagTouchForTypingMenu,
                            __SBSActivateAssistantWithContext,
                            __SBSAddAlertItemsSuppressionAssertion,
                            __SBSAddBiometricAssertion,
                            __SBSAddDownloadingIconForDisplayIdentifier,
                            __SBSAddWallpaperAnimationSuspensionAssertion,
                            __SBSAddWebClipToHomeScreen,
                            __SBSAppSwitcherServiceRegister,
                            __SBSApplicationDisplayIdentifiers,
                            __SBSAutolockTimerPostExternalChangeNotification,
                            __SBSAutolockTimerRegisterForExternalChangeNotification,
                            __SBSBundlePathForDisplayIdentifier,
                            __SBSClearWallpaperAsset,
                            __SBSDeactivateReachability, __SBSDimScreen,
                            __SBSDisplayIdentifierForPID,
                            __SBSDisplayIdentifiersForPID,
                            __SBSEnableLockScreenBundle, __SBSFlashColor,
                            __SBSFrontmostApplicationDisplayIdentifier,
                            __SBSGetActiveInterfaceOrientation,
                            __SBSGetApplicationNetworkFlags,
                            __SBSGetBatteryAwakeTime,
                            __SBSGetBatteryUsageTimesInSeconds,
                            __SBSGetCurrentBacklightFactor,
                            __SBSGetDisplayIdentifiers,
                            __SBSGetDisplayIdentifiersForExternalAccessoryProtocols,
                            __SBSGetFlattenedIconState,
                            __SBSGetHomeScreenIconMetrics, __SBSGetIconPNGData,
                            __SBSGetIconState, __SBSGetIsAlive,
                            __SBSGetMediaVolume,
                            __SBSGetNowPlayingAppBundleIdentifier,
                            __SBSGetPendingIconState, __SBSGetRecentSleepsWakes,
                            __SBSGetScheduledPowerEvents,
                            __SBSGetScreenLockStatus,
                            __SBSGetWallpaperLegibilitySettings,
                            __SBSGetWallpaperOptions, __SBSGetWallpaperPreview,
                            __SBSGetWallpaperPreviewSurface,
                            __SBSInterruptKeybagRefetch,
                            __SBSIsReachabilityEnabled, __SBSIsSystemApplication,
                            __SBSLocalizedApplicationNameForDisplayIdentifier,
                            __SBSLockDevice, __SBSLockDeviceAndFeatures,
                            __SBSOverrideDisplayedDate, __SBSPresentPowerDownUI,
                            __SBSProgrammaticSwitchAppGestureMoveToLeft,
                            __SBSProgrammaticSwitchAppGestureMoveToRight,
                            __SBSReboot, __SBSReloadIconForIdentifier,
                            __SBSRemoveWebClipFromHomeScreen,
                            __SBSRestartGetInfoForIdentifier, __SBSRestartLock,
                            __SBSRestartScheduleBlockForIdentifier,
                            __SBSRestartSetInfoForIdentifier, __SBSRestartUnlock,
                            __SBSScrollToIconWithDisplayIdentifier,
                            __SBSSetAlertSuppressionContexts,
                            __SBSSetAlertSuppressionContextsBySectionIdentifier,
                            __SBSSetAllApplicationsShowProgress,
                            __SBSSetAllApplicationsShowSyncIndicator,
                            __SBSSetAppIsConnectedToEA,
                            __SBSSetApplicationBadgeNumber,
                            __SBSSetApplicationBadgeString,
                            __SBSSetApplicationNetworkFlags,
                            __SBSSetApplicationShowsProgress,
                            __SBSSetAssistantRecognitionStrings,
                            __SBSSetIconState, __SBSSetIdleText,
                            __SBSSetInterceptsMenuButton, __SBSSetMediaVolume,
                            __SBSSetProceduralWallpaper,
                            __SBSSetReachabilityEnabled,
                            __SBSSetShowsOverridesForRecording,
                            __SBSSetShowsProgress, __SBSSetTypingActive,
                            __SBSSetVoiceControlEnabled,
                            __SBSSetVoiceRecognitionAudioInputPaths,
                            __SBSSetWallpaperImageForLocations,
                            __SBSSetWallpaperImageSurfaceForLocations,
                            __SBSSetWallpaperOptionsForLocations,
                            __SBSSetWallpaperVariant,
                            __SBSSetWantsLockButtonEvents,
                            __SBSSetWantsVolumeButtonEvents,
                            __SBSShowNetworkPromptsIfNecessary, __SBSShutDown,
                            __SBSSuspendFrontmostApp, __SBSTagTouchForTypingMenu,
                            ___SBSEventObserverGetDarwinNotificationFromEvent,
                            ___sb__mainScreenReferenceBounds,
                            ___sb__mainScreenScale, ___sb__runningInSpringBoard,
                            ___sb__setOverrideRunningInSpringBoard,
                            _kSBSAnalyticsBreadcrumbTappedKey,
                            _kSBSAnalyticsDeleteIconLocationKey,
                            _kSBSAnalyticsDeleteIconOptionsKey,
                            _kSBSAnalyticsDeleteIconSelectedOptionKey,
                            _kSBSAnalyticsDisplayLayoutElementBundleIdKey,
                            _kSBSAnalyticsDisplayLayoutElementIdentifierKey,
                            _kSBSAnalyticsDisplayLayoutElementLevelKey,
                            _kSBSAnalyticsDisplayLayoutElementUIApplicationKey,
                            _kSBSAnalyticsDisplayLayoutElementsKey,
                            _kSBSAnalyticsDoNotDisturbActiveKey,
                            _kSBSAnalyticsDoNotDisturbReasonKey,
                            _kSBSAnalyticsDockSuggestionIndexKey,
                            _kSBSAnalyticsDockSuggestionTypeKey,
                            _kSBSAnalyticsDockSuggestionsEnabledKey,
                            _kSBSAnalyticsDockSwipeGestureStateKey,
                            _kSBSAnalyticsEventTypeDashBoardCameraSwipeFailedLargestPresentExtent,
                            _kSBSAnalyticsEventTypeSpringloadedLocationKey,
                            _kSBSAnalyticsFloatingApplicationMoveGestureInitialConfigurationKey,
                            _kSBSAnalyticsFloatingApplicationMoveGestureResultConfigurationKey,
                            _kSBSAnalyticsFloatingApplicationPinGestureDidSwipeDownKey,
                            _kSBSAnalyticsFloatingApplicationPinGesturePinActionTypeKey,
                            _kSBSAnalyticsFolderStatsNumberOfFoldersInDockKey,
                            _kSBSAnalyticsFolderStatsNumberOfFoldersKey,
                            _kSBSAnalyticsFolderStatsNumberOfItemsInDockKey,
                            _kSBSAnalyticsFolderStatsNumberOfPagesKey,
                            _kSBSAnalyticsHasEverHiddenAPageKey,
                            _kSBSAnalyticsHomeScreenConfigLowDensityModeHasBeenAlteredKey,
                            _kSBSAnalyticsHomeScreenConfigLowDensityModeIsEnabledKey,
                            _kSBSAnalyticsIconDragSessionDroppedToMedusaDragStartLocationKey,
                            _kSBSAnalyticsIconDragSessionDroppedToMedusaDropActionKey,
                            _kSBSAnalyticsIconDragSessionIdentifierKey,
                            _kSBSAnalyticsIconDragSessionItemCountKey,
                            _kSBSAnalyticsIconIndexKey,
                            _kSBSAnalyticsIconIsFolderKey,
                            _kSBSAnalyticsIconLocationKey,
                            _kSBSAnalyticsIconModelWithDesiredIconStateLoadedMissingDesiredIconIdentifiersKey,
                            _kSBSAnalyticsIconPageHidingNumberOfPagesHidden,
                            _kSBSAnalyticsIconPageHidingNumberOfPagesVisible,
                            _kSBSAnalyticsLayoutStateElementBundleIdKey,
                            _kSBSAnalyticsLayoutStateElementIdentifiersKey,
                            _kSBSAnalyticsLayoutStateElementInterfaceOrientationKey,
                            _kSBSAnalyticsLayoutStateElementLayoutRoleKey,
                            _kSBSAnalyticsLayoutStateElementsKey,
                            _kSBSAnalyticsLayoutStateFloatingConfigurationKey,
                            _kSBSAnalyticsLayoutStateInterfaceOrientationKey,
                            _kSBSAnalyticsLayoutStateSpaceConfigurationKey,
                            _kSBSAnalyticsLayoutStateTransitionSourceKey,
                            _kSBSAnalyticsLayoutStateUnlockedEnvironmentKey,
                            _kSBSAnalyticsLeftBreadcrumbTypeKey,
                            _kSBSAnalyticsLockStateKey,
                            _kSBSAnalyticsNewAppDownloadLocationHasBeenSetKey,
                            _kSBSAnalyticsNewAppDownloadLocationKey,
                            _kSBSAnalyticsPIPVideoDidActivateKey,
                            _kSBSAnalyticsReachabilityCancelGestureTypeKey,
                            _kSBSAnalyticsRightBreadcrumbTypeKey,
                            _kSBSAnalyticsScreenOnKey,
                            _kSBSAnalyticsSideApplicationMoveGestureInitialConfigurationKey,
                            _kSBSAnalyticsSideApplicationMoveGestureResultConfigurationKey,
                            _kSBSAnalyticsSwipeUpFinalActionKey,
                            _kSBSAnalyticsSwipeUpLiftOffVelocityAngleKey,
                            _kSBSAnalyticsSwipeUpLiftOffVelocityXKey,
                            _kSBSAnalyticsSwipeUpLiftOffVelocityYKey,
                            _kSBSAnalyticsSwipeUpOrientationKey,
                            _kSBSAnalyticsSwipeUpPeakVelocityKey,
                            _kSBSAnalyticsSwipeUpTimestampDeltaKey,
                            _kSBSAnalyticsSwipeUpXCoordKey,
                            _kSBSAnalyticsSwipeUpYCoordKey,
                            _kSBSAnalyticsSwitcherIndexKey,
                            _kSBSAnalyticsSwitcherTypeKey,
                            _kSBSAnalyticsSystemGestureStateKey,
                            _kSBSAnalyticsSystemGestureTypeKey,
                            _kSBSAnalyticsTimestampKey,
                            _kSBSApplicationBiometricsServiceMessageKeyCredentialSet,
                            _kSBSApplicationHarmonyServiceClientMessageKeyDisplayId,
                            _kSBSApplicationHarmonyServiceServerMessageKeyWhitePointAdaptivityStyle,
                            _kSBSApplicationServiceMessageKeyBundleIdentifier,
                            _kSBSApplicationShortcutContactIconFirstNameKey,
                            _kSBSApplicationShortcutContactIconIdentifierKey,
                            _kSBSApplicationShortcutContactIconImageDataKey,
                            _kSBSApplicationShortcutContactIconLastNameKey,
                            _kSBSApplicationShortcutItemTypeSendBetaFeedback,
                            _kSBSApplicationShortcutItemTypeSendBetaFeedbackUserInfoItemIDKey,
                            _kSBSApplicationShortcutServiceClientMessageKeyDynamicApplicationShortcutItems,
                            _kSBSApplicationShortcutServiceClientMessageKeyItemTypes,
                            _kSBSApplicationShortcutServiceFetchResultComposedApplicationShortcutItemsKey,
                            _kSBSApplicationShortcutServiceFetchResultDynamicApplicationShortcutItemsKey,
                            _kSBSApplicationShortcutServiceFetchResultStaticApplicationShortcutItemsKey,
                            _kSBSApplicationShortcutServiceServerMessageKeyResult,
                            _kSBSApplicationShortcutSystemIconImageNameKey,
                            _kSBSApplicationShortcutSystemIconTypeKey,
                            _kSBSApplicationShortcutTemplateIconNameKey,
                            _kSBSCardItemBodyKey, _kSBSCardItemBundleName,
                            _kSBSCardItemCategoryIdentifierKey,
                            _kSBSCardItemIconDataKey, _kSBSCardItemIdentifierKey,
                            _kSBSCardItemRequiresPasscodeKey,
                            _kSBSCardItemSubtitleKey, _kSBSCardItemTitleKey,
                            _kSBSCardItemUserInfoKey, _kSBSDashBoardEventTypeKey,
                            _kSBSHardwareButtonServiceMessageKeyButtonKind,
                            _kSBSHardwareButtonServiceMessageKeyDeferringToken,
                            _kSBSHardwareButtonServiceMessageKeyEventMask,
                            _kSBSHardwareButtonServiceMessageKeyEventType,
                            _kSBSHardwareButtonServiceMessageKeyHIDEventsEnabled,
                            _kSBSHardwareButtonServiceMessageKeyHapticType,
                            _kSBSHardwareButtonServiceMessageKeyPriority,
                            _kSBSHardwareButtonServiceMessageKeyToggleButtonState,
                            _kSBSLockStateNotifyKey,
                            _kSBSSoftwareUpdateServiceMessageKeyPasscodePolicy,
                            _kSBSStateDumpServiceMessageKeyRemoteStateDumpTimeout,
                            _kSBSStateDumpServiceMessageKeyStateString,
                            _kSBSStateDumpServiceMessageKeyStateType,
                            _kSBSStateDumpServiceMessageKeySuccess,
                            _kSBSStateDumpServiceMessageKeySuccessDescription,
                            _kSBSStatusBarStyleOverridesAssertionExclusiveKey,
                            _kSBSStatusBarStyleOverridesAssertionOverridesKey,
                            _kSBSStatusBarStyleOverridesAssertionPIDKey,
                            _kSBSStatusBarStyleOverridesAssertionShowsWhenForegroundKey,
                            _kSBSStatusBarStyleOverridesAssertionStatusStringKey,
                            _kSBSStatusBarStyleOverridesAssertionUniqueIdentifierKey,
                            _kSBSStatusBarTapContextStyleOverrideKey,
                            _kSBSSystemBiometricsServiceMessageKeyCredentialSet,
                            _kSBSSystemHardwareButtonServiceClientMessageKeyAssertionReason,
                            _kSBSSystemHardwareButtonServiceClientMessageKeyAssertionType,
                            _kSBSSystemHardwareButtonServiceServerMessageKeyAction,
                            _kSBSTestAutomationServiceMessageKeyAnimateTransitions,
                            _kSBSTestAutomationServiceMessageKeyBlockForScreenTime,
                            _kSBSTestAutomationServiceMessageKeyBundleIdentifier,
                            _kSBSTestAutomationServiceMessageKeyEnabledState,
                            _kSBSTestAutomationServiceMessageKeyHUDIdentifier,
                            _kSBSTestAutomationServiceMessageKeyPageInteger,
                            _kSBSTestAutomationServiceMessageKeyPortSendRight,
                            _kSBSTestAutomationServiceMessageKeySceneCount,
                            _kSBSTestAutomationServiceMessageKeySizeInteger,
                            _kSBSTestAutomationServiceServiceMessageKeyPath,
                            _kSBSWallpaperServiceClientMessageKeyImageData,
                            _kSBSWallpaperServiceClientMessageKeyOrientation,
                            _kSBSWallpaperServiceClientMessageKeyRect,
                            _kSBSWallpaperServiceClientMessageKeyVariant,
                            _kSBSWallpaperServiceClientMessageKeyVideoURL,
                            _kSBSWallpaperServiceClientMessageKeyWallpaperMode,
                            _kSBUserDoneWithRequestedPasscodeUINotification,
                            _secureAppTypeName ]
    objc-classes:         [ SBIdleTimerRequestConfiguration, SBLegacyServices,
                            SBSAbstractApplicationService,
                            SBSAbstractFacilityService, SBSAbstractSystemService,
                            SBSAccessibilityWindowHostingController,
                            SBSAccessibilityWindowHostingSpecification,
                            SBSAnalyticsState, SBSAppClipService,
                            SBSAppDragLocalContext, SBSAppSwitcherSystemService,
                            SBSApplicationCarPlayService, SBSApplicationClient,
                            SBSApplicationHarmonyService,
                            SBSApplicationMultiwindowService,
                            SBSApplicationService,
                            SBSApplicationShortcutContactIcon,
                            SBSApplicationShortcutCustomImageIcon,
                            SBSApplicationShortcutIcon,
                            SBSApplicationShortcutItem,
                            SBSApplicationShortcutService,
                            SBSApplicationShortcutServiceFetchResult,
                            SBSApplicationShortcutSystemIcon,
                            SBSApplicationShortcutSystemPrivateIcon,
                            SBSApplicationShortcutTemplateIcon,
                            SBSApplicationUserQuitMonitor,
                            SBSApplicationUserQuitMonitorSessionSpecification,
                            SBSAssertion, SBSBiometricsService, SBSCardItem,
                            SBSCardItemsController, SBSDisplayLayoutElement,
                            SBSExternalDisplayLayoutElement,
                            SBSHardwareButtonService, SBSHomeScreenService,
                            SBSHomeScreenServiceSpecification,
                            SBSInCallPresentationConfiguration,
                            SBSInCallPresentationRequest,
                            SBSInCallPresentationServiceInterfaceSpecification,
                            SBSLockScreenContentAction,
                            SBSLockScreenContentAssertion,
                            SBSLockScreenPluginService,
                            SBSLockScreenRemoteContentAssertion,
                            SBSLockScreenService, SBSLockScreenServiceConnection,
                            SBSLockScreenServiceSpecification,
                            SBSMutableUserNotificationButtonDefinition,
                            SBSMutableUserNotificationTextFieldDefinition,
                            SBSRelaunchAction, SBSRemoteAlertActivationContext,
                            SBSRemoteAlertActivationOptions,
                            SBSRemoteAlertConfiguration,
                            SBSRemoteAlertConfigurationContext,
                            SBSRemoteAlertDefinition, SBSRemoteAlertHandle,
                            SBSRemoteAlertHandleContext,
                            SBSRemoteAlertHandleServiceSpecification,
                            SBSRemoteAlertHandleXPCClient,
                            SBSRemoteAlertPresentationTarget,
                            SBSRemoteContentDefinition,
                            SBSRemoteContentPreferences, SBSSecureAppAssertion,
                            SBSServiceFacilityClient, SBSSoftwareUpdateService,
                            SBSStateDumpService,
                            SBSStatusBarStyleOverridesAssertion,
                            SBSStatusBarStyleOverridesAssertionAcquisitionHandlerEntry,
                            SBSStatusBarStyleOverridesAssertionData,
                            SBSStatusBarStyleOverridesAssertionManager,
                            SBSStatusBarStyleOverridesCoordinator,
                            SBSStatusBarTapContextImpl, SBSSwitcherDemoCommands,
                            SBSSwitcherDemoCommandsSessionSpecification,
                            SBSSystemServiceClient, SBSTestAutomationService,
                            SBSUnlockOptions,
                            SBSUserNotificationButtonDefinition,
                            SBSUserNotificationTextFieldDefinition,
                            SBSWakeToRemoteAlertAssertion, SBSWallpaperClient,
                            SBSWallpaperService, SBSWebClipService,
                            SBSWebClipServiceSessionSpecification,
                            SBSWidgetMetricsServer, SBSWidgetMetricsService,
                            SBSWidgetMetricsServiceInterfaceSpecification,
                            SBScreenTimeTrackingController,
                            _SBSCarPlayApplicationInfo,
                            _SBSDisplayIdentifiersCache,
                            _SBSHardwareButtonEventConfiguration,
                            _SBSHardwareButtonEventConsumerInfo,
                            _SBSUIRemoteAlertServiceObserverHandle,
                            _SBSUserNotificationButtonDefinitionBuilder,
                            _SBSUserNotificationTextFieldDefinitionBuilder ]
    objc-ivars:           [ SBIdleTimerRequestConfiguration._boxed_maxExpirationTimeout,
                            SBIdleTimerRequestConfiguration._boxed_minExpirationTimeout,
                            SBIdleTimerRequestConfiguration._precedence,
                            SBSAbstractFacilityService._client,
                            SBSAccessibilityWindowHostingController._connection,
                            SBSAccessibilityWindowHostingController._connectionQueue,
                            SBSAccessibilityWindowHostingController._registeredWindowContextIDsToLevel,
                            SBSAnalyticsState._payload,
                            SBSAnalyticsState._timestamp,
                            SBSAppDragLocalContext._applicationBundleIdentifier,
                            SBSAppDragLocalContext._cancelsViaScaleAndFade,
                            SBSAppDragLocalContext._draggedSceneIdentifier,
                            SBSAppDragLocalContext._launchActions,
                            SBSAppDragLocalContext._launchURL,
                            SBSAppDragLocalContext._portaledPreview,
                            SBSAppDragLocalContext._sourceLocal,
                            SBSAppDragLocalContext._startLocation,
                            SBSAppDragLocalContext._userActivity,
                            SBSApplicationShortcutContactIcon._contactIdentifier,
                            SBSApplicationShortcutContactIcon._firstName,
                            SBSApplicationShortcutContactIcon._imageData,
                            SBSApplicationShortcutContactIcon._lastName,
                            SBSApplicationShortcutCustomImageIcon._dataType,
                            SBSApplicationShortcutCustomImageIcon._imageData,
                            SBSApplicationShortcutCustomImageIcon._isTemplate,
                            SBSApplicationShortcutItem._activationMode,
                            SBSApplicationShortcutItem._bundleIdentifierToLaunch,
                            SBSApplicationShortcutItem._icon,
                            SBSApplicationShortcutItem._localizedSubtitle,
                            SBSApplicationShortcutItem._localizedTitle,
                            SBSApplicationShortcutItem._targetContentIdentifier,
                            SBSApplicationShortcutItem._type,
                            SBSApplicationShortcutItem._userInfoData,
                            SBSApplicationShortcutServiceFetchResult._composedApplicationShortcutItems,
                            SBSApplicationShortcutServiceFetchResult._dynamicApplicationShortcutItems,
                            SBSApplicationShortcutServiceFetchResult._staticApplicationShortcutItems,
                            SBSApplicationShortcutSystemIcon._systemImageName,
                            SBSApplicationShortcutSystemIcon._type,
                            SBSApplicationShortcutSystemPrivateIcon._systemImageName,
                            SBSApplicationShortcutTemplateIcon._templateImageName,
                            SBSApplicationUserQuitMonitor._connection,
                            SBSApplicationUserQuitMonitor._connectionQueue,
                            SBSApplicationUserQuitMonitor._delegate,
                            SBSAssertion._assertionName, SBSAssertion._reason,
                            SBSAssertion._receiveRight, SBSCardItem._body,
                            SBSCardItem._bundleName,
                            SBSCardItem._categoryIdentifier,
                            SBSCardItem._iconData, SBSCardItem._identifier,
                            SBSCardItem._requiresPasscode, SBSCardItem._subtitle,
                            SBSCardItem._thumbnail, SBSCardItem._title,
                            SBSCardItem._userInfo,
                            SBSCardItemsController._connected,
                            SBSCardItemsController._connection,
                            SBSCardItemsController._identifier,
                            SBSHardwareButtonService._buttonConfigurationsPerKind,
                            SBSHardwareButtonService._consumersPerKind,
                            SBSHardwareButtonService._requestHIDAssertionsPerKind,
                            SBSHomeScreenService._connection,
                            SBSHomeScreenService._connectionQueue,
                            SBSInCallPresentationConfiguration._preferredBannerHeight,
                            SBSInCallPresentationConfiguration._preferredPresentationMode,
                            SBSInCallPresentationConfiguration._sceneBundleIdentifier,
                            SBSInCallPresentationConfiguration._userInitiated,
                            SBSLockScreenContentAction._configurationObject,
                            SBSLockScreenContentAction._secureAppType,
                            SBSLockScreenContentAction._slot,
                            SBSLockScreenContentAction._type,
                            SBSLockScreenContentAssertion._action,
                            SBSLockScreenContentAssertion._configurationObject,
                            SBSLockScreenContentAssertion._errorHandler,
                            SBSLockScreenContentAssertion._identifier,
                            SBSLockScreenContentAssertion._slot,
                            SBSLockScreenService._lock,
                            SBSLockScreenService._lock_connection,
                            SBSLockScreenServiceConnection._connection,
                            SBSLockScreenServiceConnection._lock,
                            SBSLockScreenServiceConnection._lock_preventPasscodeLockReasons,
                            SBSLockScreenServiceConnection._lock_preventSpuriousScreenUndimReasons,
                            SBSRemoteAlertActivationContext._actions,
                            SBSRemoteAlertActivationContext._activatingForSiri,
                            SBSRemoteAlertActivationContext._activityContinuationIdentifier,
                            SBSRemoteAlertActivationContext._legacyAlertOptions,
                            SBSRemoteAlertActivationContext._presentationTarget,
                            SBSRemoteAlertActivationContext._reason,
                            SBSRemoteAlertActivationContext._shouldInvalidateWhenDeactivated,
                            SBSRemoteAlertActivationContext._switcherEligible,
                            SBSRemoteAlertActivationContext._userInfo,
                            SBSRemoteAlertConfigurationContext._actions,
                            SBSRemoteAlertConfigurationContext._legacyAlertOptions,
                            SBSRemoteAlertConfigurationContext._userInfo,
                            SBSRemoteAlertConfigurationContext._xpcEndpoint,
                            SBSRemoteAlertDefinition._forCarPlay,
                            SBSRemoteAlertDefinition._impersonatedCarPlayAppIdentifier,
                            SBSRemoteAlertDefinition._serviceName,
                            SBSRemoteAlertDefinition._userInfo,
                            SBSRemoteAlertDefinition._viewControllerClassName,
                            SBSRemoteAlertHandle._calloutSerialQueue,
                            SBSRemoteAlertHandle._handleClient,
                            SBSRemoteAlertHandle._handleID,
                            SBSRemoteAlertHandle._lock,
                            SBSRemoteAlertHandle._lock_active,
                            SBSRemoteAlertHandle._lock_observers,
                            SBSRemoteAlertHandle._lock_valid,
                            SBSRemoteAlertHandleContext._active,
                            SBSRemoteAlertHandleContext._handleID,
                            SBSRemoteAlertHandleXPCClient._calloutQueue,
                            SBSRemoteAlertHandleXPCClient._connection,
                            SBSRemoteAlertHandleXPCClient._connectionAccessQueue,
                            SBSRemoteAlertHandleXPCClient._connectionActivated,
                            SBSRemoteAlertHandleXPCClient._connectionInvalidated,
                            SBSRemoteAlertHandleXPCClient._handleIDToHandle,
                            SBSRemoteAlertPresentationTarget._shouldDismissOnUILock,
                            SBSRemoteAlertPresentationTarget._targetProcess,
                            SBSRemoteContentDefinition._serviceName,
                            SBSRemoteContentDefinition._userInfo,
                            SBSRemoteContentDefinition._viewControllerClassName,
                            SBSRemoteContentDefinition._xpcEndpoint,
                            SBSRemoteContentPreferences._backgroundStyle,
                            SBSRemoteContentPreferences._dateTimeStyle,
                            SBSRemoteContentPreferences._homeGestureMode,
                            SBSRemoteContentPreferences._reducesWhitePoint,
                            SBSRemoteContentPreferences._suppressesBottomEdgeContent,
                            SBSRemoteContentPreferences._suppressesNotifications,
                            SBSSecureAppAssertion._actualAssertion,
                            SBSServiceFacilityClient._numberOfCheckOuts,
                            SBSStatusBarStyleOverridesAssertion._assertionData,
                            SBSStatusBarStyleOverridesAssertion._invalidationHandler,
                            SBSStatusBarStyleOverridesAssertionAcquisitionHandlerEntry._handler,
                            SBSStatusBarStyleOverridesAssertionAcquisitionHandlerEntry._queue,
                            SBSStatusBarStyleOverridesAssertionData._exclusive,
                            SBSStatusBarStyleOverridesAssertionData._pid,
                            SBSStatusBarStyleOverridesAssertionData._showsWhenForeground,
                            SBSStatusBarStyleOverridesAssertionData._statusBarStyleOverrides,
                            SBSStatusBarStyleOverridesAssertionData._statusString,
                            SBSStatusBarStyleOverridesAssertionData._uniqueIdentifier,
                            SBSStatusBarStyleOverridesAssertionManager._acquisitionHandlerEntriesByIdentifier,
                            SBSStatusBarStyleOverridesAssertionManager._assertionsByIdentifier,
                            SBSStatusBarStyleOverridesAssertionManager._coordinatorCalloutQueue,
                            SBSStatusBarStyleOverridesAssertionManager._internalQueue,
                            SBSStatusBarStyleOverridesAssertionManager._internalQueue_styleOverrideCoordinator,
                            SBSStatusBarStyleOverridesAssertionManager._sbXPCConnection,
                            SBSStatusBarStyleOverridesCoordinator._delegate,
                            SBSStatusBarStyleOverridesCoordinator._styleOverrides,
                            SBSStatusBarTapContextImpl._styleOverride,
                            SBSSwitcherDemoCommands._connection,
                            SBSSwitcherDemoCommands._connectionQueue,
                            SBSSystemServiceClient._buttonEventServiceIsWaitingForServerMessages,
                            SBSUnlockOptions._aboveOtherContexts,
                            SBSUserNotificationButtonDefinition._isPreferredButton,
                            SBSUserNotificationButtonDefinition._presentationStyle,
                            SBSUserNotificationButtonDefinition._title,
                            SBSUserNotificationTextFieldDefinition._autocapitalizationType,
                            SBSUserNotificationTextFieldDefinition._autocorrectionType,
                            SBSUserNotificationTextFieldDefinition._hasSetMaxLength,
                            SBSUserNotificationTextFieldDefinition._isSecure,
                            SBSUserNotificationTextFieldDefinition._keyboardType,
                            SBSUserNotificationTextFieldDefinition._maxLength,
                            SBSUserNotificationTextFieldDefinition._title,
                            SBSUserNotificationTextFieldDefinition._value,
                            SBSWallpaperService._callbackQueue,
                            SBSWallpaperService._client,
                            SBSWallpaperService._wasInvalidated,
                            SBSWidgetMetricsServer._connection,
                            SBSWidgetMetricsService._metricsProvider,
                            SBScreenTimeTrackingController._layoutMonitor,
                            SBScreenTimeTrackingController._queue,
                            SBScreenTimeTrackingController._queue_activeCategory,
                            SBScreenTimeTrackingController._queue_activeContext,
                            SBScreenTimeTrackingController._queue_isPhoneOrFaceTimeActive,
                            SBScreenTimeTrackingController._queue_isScreenOn,
                            SBScreenTimeTrackingController._queue_lastCategoryChangeTime,
                            SBScreenTimeTrackingController._queue_lastLayout,
                            SBScreenTimeTrackingController._queue_lastLayoutTransitionContext,
                            SBScreenTimeTrackingController._queue_thisCategoryStartTime,
                            _SBSCarPlayApplicationInfo._iconImageData,
                            _SBSCarPlayApplicationInfo._iconImageScale,
                            _SBSCarPlayApplicationInfo._localizedDisplayName,
                            _SBSDisplayIdentifiersCache._changedBlock,
                            _SBSDisplayIdentifiersCache._changedToken,
                            _SBSDisplayIdentifiersCache._displayIdentifiers,
                            _SBSDisplayIdentifiersCache._queue,
                            _SBSHardwareButtonEventConfiguration._eventMask,
                            _SBSHardwareButtonEventConfiguration._maximumPriority,
                            _SBSHardwareButtonEventConsumerInfo._buttonKind,
                            _SBSHardwareButtonEventConsumerInfo._consumer,
                            _SBSHardwareButtonEventConsumerInfo._eventMask,
                            _SBSHardwareButtonEventConsumerInfo._eventPriority,
                            _SBSHardwareButtonEventConsumerInfo._service,
                            _SBSHardwareButtonEventConsumerInfo._valid,
                            _SBSUIRemoteAlertServiceObserverHandle._activationHandler,
                            _SBSUIRemoteAlertServiceObserverHandle._deactivationHandler,
                            _SBSUserNotificationButtonDefinitionBuilder._definitions,
                            _SBSUserNotificationTextFieldDefinitionBuilder._definitions ]
...

---
Name: MetalKit
Functions:
- Name: MTKMetalVertexDescriptorFromModelIOWithError
  SwiftPrivate: true
- Name: MTKModelIOVertexDescriptorFromMetalWithError
  SwiftPrivate: true
Classes:
- Name: MTKMesh
  Methods:
  - Selector: "newMeshesFromAsset:device:sourceMeshes:error:"
    MethodKind: Class
    SwiftPrivate: true
- Name: MTKTextureLoader
  Methods:
  - Selector: "newTextureWithContentsOfURL:options:completionHandler:"
    SwiftName: newTexture(URL:options:completionHandler:)
    MethodKind: Instance
  - Selector: "newTextureWithName:scaleFactor:bundle:options:completionHandler:"
    MethodKind: Instance
    SwiftName: newTexture(name:scaleFactor:bundle:options:completionHandler:)
  - Selector: "newTextureWithName:scaleFactor:displayGamut:bundle:options:completionHandler:"
    SwiftName: newTexture(name:scaleFactor:displayGamut:bundle:options:completionHandler:)
    MethodKind: Instance
  - Selector: "newTexturesWithContentsOfURLs:options:completionHandler:"
    SwiftName: newTextures(URLs:options:completionHandler:)
    MethodKind: Instance
  - Selector: "newTexturesWithNames:scaleFactor:bundle:options:completionHandler:"
    SwiftName: newTextures(names:scaleFactor:bundle:options:completionHandler:)
    MethodKind: Instance
  - Selector: "newTexturesWithNames:scaleFactor:displayGamut:bundle:options:completionHandler:"
    SwiftName: newTextures(names:scaleFactor:displayGamut:bundle:options:completionHandler:)
    MethodKind: Instance
  - Selector: "newTextureWithData:options:completionHandler:"
    SwiftName: newTexture(data:options:completionHandler:)
    MethodKind: Instance
  - Selector: "newTextureWithCGImage:options:completionHandler:"
    SwiftName: newTexture(cgImage:options:completionHandler:)
    MethodKind: Instance
  - Selector: "newTextureWithMDLTexture:options:completionHandler:"
    SwiftName: newTexture(texture:options:completionHandler:)
    MethodKind: Instance
  - Selector: "newTextureWithContentsOfURL:options:error:"
    SwiftName: newTexture(URL:options:)
    MethodKind: Instance
  - Selector: "newTexturesWithContentsOfURLs:options:error:"
    SwiftName: newTextures(URLs:options:error:)
    MethodKind: Instance
  - Selector: "newTextureWithData:options:error:"
    SwiftName: newTexture(data:options:)
    MethodKind: Instance
  - Selector: "newTextureWithCGImage:options:error:"
    SwiftName: newTexture(cgImage:options:)
    MethodKind: Instance
  - Selector: "newTextureWithMDLTexture:options:error:"
    SwiftName: newTexture(texture:options:)
    MethodKind: Instance
  - Selector: "newTextureWithName:scaleFactor:bundle:options:error:"
    SwiftName: newTexture(name:scaleFactor:bundle:options:)
    MethodKind: Instance
  - Selector: "newTextureWithName:scaleFactor:displayGamut:bundle:options:error:"
    SwiftName: newTexture(name:scaleFactor:displayGamut:bundle:options:)
    MethodKind: Instance

SwiftVersions:
- Version: 3
  Typedefs:
  - Name: MTKTextureLoaderCubeLayout
    SwiftWrapper: none
  - Name: MTKTextureLoaderError
    SwiftWrapper: none
  - Name: MTKTextureLoaderOption
    SwiftWrapper: none
  - Name: MTKTextureLoaderOrigin
    SwiftWrapper: none
  - Name: MTKModelError
    SwiftWrapper: none
  - Name: MTKTextureLoaderCallback
    SwiftName: "MTKTextureLoaderCallback"
  - Name: MTKTextureLoaderArrayCallback
    SwiftName: "MTKTextureLoaderArrayCallback"
  Functions:
  - Name: MTKMetalVertexDescriptorFromModelIO
    NullabilityOfRet: N
  - Name: MTKMetalVertexDescriptorFromModelIOWithError
    NullabilityOfRet: N
    SwiftPrivate: false
  - Name: MTKModelIOVertexDescriptorFromMetalWithError
    SwiftPrivate: false
  Classes:
  - Name: MTKMesh
    Methods:
    - Selector: "newMeshesFromAsset:device:sourceMeshes:error:"
      MethodKind: Class
      SwiftPrivate: false
  - Name: MTKTextureLoader
    Methods:
    - Selector: "newTextureWithContentsOfURL:options:completionHandler:"
      MethodKind: Instance
      Parameters:
      - Position: 1
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithName:scaleFactor:bundle:options:completionHandler:"
      MethodKind: Instance
      Parameters:
      - Position: 3
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithName:scaleFactor:displayGamut:bundle:options:completionHandler:"
      MethodKind: Instance
      Parameters:
      - Position: 4
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTexturesWithContentsOfURLs:options:completionHandler:"
      MethodKind: Instance
      Parameters:
      - Position: 1
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTexturesWithNames:scaleFactor:bundle:options:completionHandler:"
      MethodKind: Instance
      Parameters:
      - Position: 3
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTexturesWithNames:scaleFactor:displayGamut:bundle:options:completionHandler:"
      MethodKind: Instance
      Parameters:
      - Position: 2
        Type: "_Nullable CGColorSpaceRef"
      - Position: 4
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithData:options:completionHandler:"
      MethodKind: Instance
      Parameters:
      - Position: 1
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithCGImage:options:completionHandler:"
      MethodKind: Instance
      Parameters:
      - Position: 1
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithMDLTexture:options:completionHandler:"
      MethodKind: Instance
      Parameters:
      - Position: 1
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithContentsOfURL:options:error:"
      MethodKind: Instance
      Parameters:
      - Position: 1
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTexturesWithContentsOfURLs:options:error:"
      MethodKind: Instance
      Parameters:
      - Position: 1
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithData:options:error:"
      MethodKind: Instance
      Parameters:
      - Position: 1
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithCGImage:options:error:"
      MethodKind: Instance
      Parameters:
      - Position: 1
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithMDLTexture:options:error:"
      MethodKind: Instance
      Parameters:
      - Position: 1
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithName:scaleFactor:bundle:options:error:"
      MethodKind: Instance
      Parameters:
      - Position: 3
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"
    - Selector: "newTextureWithName:scaleFactor:displayGamut:bundle:options:error:"
      MethodKind: Instance
      Parameters:
      - Position: 2
        Type: "_Nullable CGColorSpaceRef"
      - Position: 4
        Type: "NSDictionary <MTKTextureLoaderOption, NSObject *> * _Nullable"

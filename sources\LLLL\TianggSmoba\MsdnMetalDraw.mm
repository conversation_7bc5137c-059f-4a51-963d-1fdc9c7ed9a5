//
//  MsdnMetalDraw.m
//  MsdnHookGame
//
//  Created by 梦三大牛 on 2023/2/27.
//

#import "MsdnMetalDraw.h"
//#import "MsUIWindow.h"
#import "TianggSmoba.h"
id <MTLTexture> texture;
id <MTLDevice> device;
typedef  struct _HeroImg{
    ImTextureID tex;
    int32_t HeroID;
}HeroImg;
static std::vector<HeroImg>HeroImgList;

extern void MsDrawText(char *str, ImVec2 pos, bool isCentered, int color, bool outline, float fontSize){
    ImVec2 vec2 = pos;
    if (isCentered) {
        ImFont* font = ImGui::GetFont();
        font->Scale = 20.f / font->FontSize;
        
        ImVec2 textSize = font->CalcTextSizeA(fontSize, MAXFLOAT, 0.0f, str);
        vec2.x -= textSize.x * 0.5f;
    }
    if (outline){
        ImU32 outlineColor = 0xFF000000;
        ImGui::GetOverlayDrawList()->AddText(ImGui::GetFont(), fontSize, ImVec2(vec2.x + 1, vec2.y + 1), outlineColor, str);
        ImGui::GetOverlayDrawList()->AddText(ImGui::GetFont(), fontSize, ImVec2(vec2.x - 1, vec2.y - 1), outlineColor, str);
        ImGui::GetOverlayDrawList()->AddText(ImGui::GetFont(), fontSize, ImVec2(vec2.x + 1, vec2.y - 1), outlineColor, str);
        ImGui::GetOverlayDrawList()->AddText(ImGui::GetFont(), fontSize, ImVec2(vec2.x - 1, vec2.y + 1), outlineColor, str);
    }
    ImGui::GetOverlayDrawList()->AddText(ImGui::GetFont(), fontSize, vec2, color, str);
}
@implementation MsRenderer
-(instancetype)initWithMetalKitView:(MTKView *)mtkView{
    self = [super init];
    if (self) {
        device = mtkView.device;
        _commandQueue = [device newCommandQueue];
        IMGUI_CHECKVERSION();
        ImGui::CreateContext();
        ImGuiIO& io = ImGui::GetIO(); (void)io;
        ImGui::StyleColorsClassic();
        
//        io.Fonts->AddFontFromMemoryTTF((void *)baidu_font_data, baidu_font_size, 13, NULL, io.Fonts->GetGlyphRangesChineseFull());//免越狱版字体
//        io.Fonts->AddFontFromFileTTF("/System/Library/Fonts/LanguageSupport/PingFang.ttc", 13,NULL,io.Fonts->GetGlyphRangesChineseFull());//越狱版字体
        ImGui_ImplMetal_Init(device);
    }
    return self;
    
}

- (void)drawInMTKView:(MTKView*)view{
    ImGuiIO& io = ImGui::GetIO();
    CGFloat framebufferScale =  UIScreen.mainScreen.scale;
    io.DisplayFramebufferScale = ImVec2(framebufferScale, framebufferScale);
    io.DeltaTime = 1 / float(view.preferredFramesPerSecond ?: 60);
    io.DisplaySize.x = view.currentDrawable.layer.drawableSize.width/framebufferScale;
    io.DisplaySize.y = view.currentDrawable.layer.drawableSize.height/framebufferScale;
    
    id<MTLCommandBuffer> commandBuffer = [self.commandQueue commandBuffer];
    static float clear_color[4] = { 0, 0, 0, 0 };
    
    MTLRenderPassDescriptor* renderPassDescriptor = view.currentRenderPassDescriptor;
    if (renderPassDescriptor != nil)
    {
        renderPassDescriptor.colorAttachments[0].clearColor = MTLClearColorMake(clear_color[0], clear_color[1], clear_color[2], clear_color[3]);
        
        id <MTLRenderCommandEncoder> renderEncoder = [commandBuffer renderCommandEncoderWithDescriptor:renderPassDescriptor];
        
        [renderEncoder pushDebugGroup:@"ImGui demo"];
        
        ImGui_ImplMetal_NewFrame(renderPassDescriptor);
        ImGui::NewFrame();
        
//        MsDrawText((char*)"测试测试测试", ImVec2(MsKuan/2, 10), true, 0xFFFFFFFF, true, 15);
        if(MsGetAppStartStatus((char*)"王者荣耀"))TianggSmobaReadFunction();
        
        ImGui::GetForegroundDrawList()->PushClipRectFullScreen();
        ImGui::Render();
        ImDrawData* draw_data = ImGui::GetDrawData();
        ImGui_ImplMetal_RenderDrawData(draw_data, commandBuffer, renderEncoder);
        
        [renderEncoder popDebugGroup];
        [renderEncoder endEncoding];
        [commandBuffer presentDrawable:view.currentDrawable];
    }
    
    [commandBuffer commit];
}

- (void)mtkView:(nonnull MTKView *)view drawableSizeWillChange:(CGSize)size {
    
}
@end

sdkafowanbnonowaf * Global_DrawView;
MsRenderer *g_renderer;
@implementation sdkafowanbnonowaf

- (void)WzInit:(UIViewController*) faUIViewController {
    Global_DrawView = [[sdkafowanbnonowaf alloc] initWithFrame:faUIViewController.view.frame];
    Global_DrawView.device = MTLCreateSystemDefaultDevice();
    Global_DrawView.preferredFramesPerSecond = 120;
    g_renderer = [[MsRenderer alloc] initWithMetalKitView:Global_DrawView];
    Global_DrawView.delegate = g_renderer;
    [Global_DrawView setBackgroundColor:[UIColor clearColor]];
    //==============初始化过直播视图===============
    UITextField *MsZhiBoMetalView = [[UITextField alloc]initWithFrame:Global_DrawView.frame];
    MsZhiBoMetalView.backgroundColor = [UIColor clearColor];
    MsZhiBoMetalView.secureTextEntry = NO;
    MsZhiBoMetalView.userInteractionEnabled = YES;
    [MsZhiBoMetalView.subviews.firstObject addSubview:Global_DrawView];//添加Metal视图
    [faUIViewController.view addSubview:MsZhiBoMetalView];
}

-(id)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
    UIView *hitView = [super hitTest:point withEvent:event];
    if (hitView == self){
        return nil;
    }else {
        return hitView;
    }
}
- (void)updateIOWithTouchEvent:(UIEvent *)event{
    UITouch *anyTouch = event.allTouches.anyObject;
    CGPoint touchLocation = [anyTouch locationInView:self];
    ImGuiIO &io = ImGui::GetIO();
    io.MousePos = ImVec2(touchLocation.x, touchLocation.y);
    
    BOOL hasActiveTouch = NO;
    for (UITouch *touch in event.allTouches)
    {
        if (touch.phase != UITouchPhaseEnded && touch.phase != UITouchPhaseCancelled)
        {
            hasActiveTouch = YES;
            break;
        }
    }
    io.MouseDown[0] = hasActiveTouch;
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [self updateIOWithTouchEvent:event];
    //[[UIApplication sharedApplication].keyWindow touchesBegan:touches withEvent:event];
}

- (void)touchesMoved:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [self updateIOWithTouchEvent:event];
  // [[UIApplication sharedApplication].keyWindow touchesMoved:touches withEvent:event];
}

- (void)touchesCancelled:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [self updateIOWithTouchEvent:event];
   //[[UIApplication sharedApplication].keyWindow touchesCancelled:touches withEvent:event];
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [self updateIOWithTouchEvent:event];
    //[[UIApplication sharedApplication].keyWindow touchesEnded:touches withEvent:event];
}
@end

//
//  MsdnMetalDraw.h
//  MsdnHookGame
//
//  Created by 梦三大牛 on 2023/2/27.
//
#import "TianggSmoba.h"
#import <Metal/Metal.h>
#import <MetalKit/MetalKit.h>
#include <Msdnimgui/imgui.h>
#include <Msdnimgui/imgui_impl_metal.h>
#ifndef MsdnMetalDraw_h
#define MsdnMetalDraw_h
#define MsKuan  [UIScreen mainScreen].bounds.size.width //1194
#define MsGao [UIScreen mainScreen].bounds.size.height //834
extern id <MTLTexture> texture;
extern id <MTLDevice> device;
extern void MsDrawText(char *str, ImVec2 pos, bool isCentered, int color, bool outline, float fontSize);
#endif /* MsdnMetalDraw_h */

@interface MsRenderer : NSObject<MTKViewDelegate>
@property (nonatomic, strong) id <MTLCommandQueue> commandQueue;
@property (nonatomic, strong) MTKView *mtkView;
-(instancetype)initWithMetalKitView:(MTKView *)mtkView;
@end

@interface sdkafowanbnonowaf : MTKView
extern sdkafowanbnonowaf * Global_DrawView;
extern MsRenderer *g_renderer;
- (void)WzInit:(UIViewController*) faUIViewController;
@end

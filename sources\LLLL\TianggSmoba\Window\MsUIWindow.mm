//
//  MsUIWindow.m
//  MsUIWindow
//
//  Created by 梦三大牛 on 2022/12/22.
//

#import "MsUIWindow.h"
#import "MsHomeMenuController.h"
#import "MsdnMetalDraw.h"
@implementation MsUIWindow
MsUIWindow *MsDrawWindow;
static id _sharedInstance;
static dispatch_once_t _onceToken;

- (instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if (self){
        self.userInteractionEnabled = NO;
        self.windowLevel = UIWindowLevelStatusBar;
        self.clipsToBounds = YES;
        
        [self setHidden:NO];
        [self setAlpha:1.0];
        [self setBackgroundColor:[UIColor clearColor]];
    }
    return self;
}


- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event{
    UIView *view = [super hitTest:point withEvent:event];
    if (view == self.rootViewController.view) {
        return nil;
    }
    return view;
}

- (BOOL)_ignoresHitTest {
    return YES;
}

+ (instancetype)sharedInstance{
    dispatch_once(&_onceToken, ^{
        _sharedInstance = [[self alloc] initWithFrame:[UIScreen mainScreen].bounds];
    });
    return _sharedInstance;
}

@end
NSTimer *MsShuaXin;

// 初始化绘制窗口
//static void __attribute__((constructor)) MsdnLoadFunction(){
//   // =============先加载绘制MsUIWindow视图并获取到屏幕转向
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        if ([UIApplication sharedApplication].statusBarOrientation == UIInterfaceOrientationLandscapeLeft || [UIApplication sharedApplication].statusBarOrientation == UIInterfaceOrientationLandscapeRight){
//            //======================初始化==横屏==绘制Window======================
//            MsDrawWindow = [MsUIWindow sharedInstance];
//            MsDrawWindow.frame = CGRectMake(0, 0, MsKuan, MsGao);
//            MsDrawWindow.center = CGPointMake(MsKuan/2, MsGao/2);
//            MsDrawWindow.transform = CGAffineTransformMakeRotation(M_PI/1.5);
//
//        }else{
//            //======================初始化==竖屏==绘制Window======================
//            MsDrawWindow = [MsUIWindow sharedInstance];
//            MsDrawWindow.frame = CGRectMake(0, 0, MsGao, MsKuan);
//            MsDrawWindow.center = CGPointMake(MsKuan/2 ,MsGao/2);
//            MsDrawWindow.transform = CGAffineTransformMakeRotation(M_PI/2);
//        }
//        MsDrawWindow.userInteractionEnabled = YES;
//        MsDrawWindow.autoresizingMask= UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
//        MsDrawWindow.rootViewController = [[MsHomeMenuController alloc]init];
//    });
//}


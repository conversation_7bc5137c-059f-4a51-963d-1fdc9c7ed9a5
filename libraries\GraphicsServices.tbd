--- !tapi-tbd-v3
archs:                 [ armv7, armv7s, arm64, arm64e ]
platform:              ios
flags:                 [ flat_namespace ]
install-name:          /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
current-version:       14
compatibility-version: 1
objc-constraint:       retain_release
exports:
  - archs:                [ armv7, armv7s, arm64, arm64e ]
    symbols:              [ _GSColorCreateColorWithDeviceRGBA,
                            _GSColorCreateColorWithDeviceRGBAInfo,
                            _GSColorCreateWithDeviceWhite,
                            _GSColorGetRGBAComponents, _GSColorGetRGBAInfo,
                            _GSCopyPurpleNamedPerPIDPort, _GSCopyPurpleNamedPort,
                            _GSCurrentEventTimestamp, _GSEventAccelerometerAxisX,
                            _GSEventAccelerometerAxisY,
                            _GSEventAccelerometerAxisZ,
                            _GSEventAccessoryAvailabilityChanged, _GSEventCopy,
                            _GSEventCopyCharacters,
                            _GSEventCopyCharactersIgnoringModifiers,
                            _GSEventCopyMarkedCharacters,
                            _GSEventCreateAccessoryKeyStateEvent,
                            _GSEventCreateApplicationSuspendEvent,
                            _GSEventCreateKeyEvent,
                            _GSEventCreatePlistRepresentation,
                            _GSEventCreateWithEventRecord,
                            _GSEventCreateWithPlist, _GSEventDeviceOrientation,
                            _GSEventDisableHandEventCoalescing,
                            _GSEventFinishedActivating,
                            _GSEventGetAccessoryKeyStateInfo,
                            _GSEventGetCharacterSet, _GSEventGetClickCount,
                            _GSEventGetDeltaX, _GSEventGetDeltaY,
                            _GSEventGetHIDTimestamp, _GSEventGetHandInfo,
                            _GSEventGetHardwareKeyboardCountry,
                            _GSEventGetHardwareKeyboardType,
                            _GSEventGetInnerMostPathPosition, _GSEventGetKeyCode,
                            _GSEventGetKeyFlags, _GSEventGetLocationInWindow,
                            _GSEventGetModifierFlags,
                            _GSEventGetOuterMostPathPosition,
                            _GSEventGetPathInfoAtIndex, _GSEventGetSenderPID,
                            _GSEventGetSubType, _GSEventGetTimestamp,
                            _GSEventGetType, _GSEventGetTypeID,
                            _GSEventGetUsagePage, _GSEventGetWindow,
                            _GSEventInitialize, _GSEventInitializeAsExtension,
                            _GSEventInitializeWorkspaceWithQueue,
                            _GSEventIsChordingHandEvent,
                            _GSEventIsForceQuitEvent, _GSEventIsHandEvent,
                            _GSEventIsHardwareKeyboardAttached,
                            _GSEventIsHardwareKeyboardEvent,
                            _GSEventIsKeyRepeating, _GSEventIsTabKeyEvent,
                            _GSEventLockDevice, _GSEventPopRunLoopMode,
                            _GSEventPushRunLoopMode,
                            _GSEventQueueContainsMouseEvent,
                            _GSEventQuitTopApplication,
                            _GSEventReceiveRunLoopMode,
                            _GSEventRegisterEventCallBack,
                            _GSEventRemoveShouldRouteToFrontMost,
                            _GSEventResetIdleTimer, _GSEventRun, _GSEventRunModal,
                            _GSEventSendApplicationOpenURL, _GSEventSendKeyEvent,
                            _GSEventSetBacklightLevel, _GSEventSetCharacters,
                            _GSEventSetHardwareKeyboardAttached,
                            _GSEventSetHardwareKeyboardAttachedWithCountryCodeAndType,
                            _GSEventSetLocationInWindow,
                            _GSEventSetPathInfoAtIndex, _GSEventSetType,
                            _GSEventShouldRouteToFrontMost,
                            _GSEventSourceIsHardware, _GSEventStopModal,
                            _GSEventStopVibrator, _GSEventVibrateForDuration,
                            _GSFontCopyFamilyNames, _GSFontCopyFontFilePath,
                            _GSFontCopyFontNamesForFamilyName,
                            _GSFontCopyNormalizedAdditionalFontName,
                            _GSFontCopyPersistentPostscriptURL,
                            _GSFontCreateWithName, _GSFontGetCacheDictionary,
                            _GSFontInitialize, _GSFontPurgeFontCache,
                            _GSFontRegisterCGFont, _GSFontRegisterPersistentURLs,
                            _GSFontRegisterURL, _GSFontUnregisterCGFont,
                            _GSFontUnregisterPersistentURLs,
                            _GSFontUnregisterURL, _GSGetPurpleApplicationPort,
                            _GSGetPurpleSystemAppPort,
                            _GSGetPurpleSystemEventPort,
                            _GSGetPurpleWorkspacePort, _GSGetTimeEventHandling,
                            _GSInitialize, _GSKeyboardCreate,
                            _GSKeyboardCreateWithCountryCode,
                            _GSKeyboardGetHWKeyboardType,
                            _GSKeyboardGetKeyCodeForChar, _GSKeyboardGetLayout,
                            _GSKeyboardGetLiveModifierState,
                            _GSKeyboardGetModifierState,
                            _GSKeyboardGetStickyLockModifierState,
                            _GSKeyboardGetTranslationOptions,
                            _GSKeyboardGetTypeID,
                            _GSKeyboardHWKeyboardLayoutsPlist,
                            _GSKeyboardHWKeyboardNormalizeInput,
                            _GSKeyboardRelease, _GSKeyboardReset,
                            _GSKeyboardSetHardwareKeyboardAttached,
                            _GSKeyboardSetLayout,
                            _GSKeyboardSetTranslationOptions,
                            _GSKeyboardTranslateKey,
                            _GSKeyboardTranslateKeyExtended,
                            _GSKeyboardTranslateKeyExtendedCommand,
                            _GSKeyboardTranslateKeyWithModifiers,
                            _GSMainScreenOrientation, _GSMainScreenPixelSize,
                            _GSMainScreenPointSize,
                            _GSMainScreenPositionTransform,
                            _GSMainScreenScaleFactor, _GSMainScreenSize,
                            _GSMainScreenWindowTransform,
                            _GSRegisterPurpleNamedPerPIDPort,
                            _GSRegisterPurpleNamedPort,
                            _GSSaveEventHandlingTimes,
                            _GSSendAppPreferencesChanged,
                            _GSSendApplicationFinishedBackgroundContentFetchingEvent,
                            _GSSendApplicationFinishedBackgroundContentFetchingEventWithSequenceNumber,
                            _GSSendApplicationFinishedBackgroundNotificationActionEvent,
                            _GSSendApplicationSuspendEvent,
                            _GSSendApplicationSuspendedSettingsUpdatedEvent,
                            _GSSendEvent, _GSSendSimpleEvent,
                            _GSSendSimpleEventWithSubtype, _GSSendSystemAppEvent,
                            _GSSendSystemEvent, _GSSendWorkspaceEvent,
                            _GSSetMainScreenInfo, _GSSetTimeEventHandling,
                            _GSSystemHasCapability, _GSSystemRootDirectory,
                            _GSSystemSetRequiresCapabilities,
                            __GSEventGetGSEventRecord, _kGS3GVeniceCapability,
                            _kGS720pPlaybackCapability,
                            _kGSARMV6ExecutionCapability,
                            _kGSARMV7ExecutionCapability,
                            _kGSAccelerometerCapability,
                            _kGSAccessibilityCapability,
                            _kGSAdditionalTextTonesCapability,
                            _kGSAmbientLightSensorCapability,
                            _kGSAppleInternalInstallCapability,
                            _kGSAssistantCapability,
                            _kGSAutoFocusCameraCapability,
                            _kGSBluetoothCapability, _kGSCameraCapability,
                            _kGSCameraFlashCapability, _kGSCameraRestriction,
                            _kGSCapabilityChangedNotification,
                            _kGSCapabilityUpdateNotification,
                            _kGSCellularDataCapability,
                            _kGSCellularTelephonyCapability,
                            _kGSContainsCellularRadioCapability,
                            _kGSDataPlanCapability,
                            _kGSDelaySleepForHeadsetClickCapability,
                            _kGSDeviceNameString, _kGSDictationCapability,
                            _kGSDisplayFCCLogosViaSoftwareCapability,
                            _kGSDisplayIdentifiersCapability,
                            _kGSDisplayMirroringCapability,
                            _kGSDisplayPortCapability, _kGSEncodeAACCapability,
                            _kGSEncryptedDataPartitionCapability,
                            _kGSEnforceCameraShutterClick, _kGSEnforceGoogleMail,
                            _kGSEventHardwareKeyboardAvailabilityChangedNotification,
                            _kGSExplicitContentRestriction,
                            _kGSFrontFacingCameraCapability,
                            _kGSFull6FeaturesCapability, _kGSGPSCapability,
                            _kGSGameKitCapability, _kGSGasGaugeBatteryCapability,
                            _kGSGreenTeaDeviceCapability,
                            _kGSGyroscopeCapability, _kGSH264EncoderCapability,
                            _kGSHDRImageCaptureCapability,
                            _kGSHDVideoCaptureCapability,
                            _kGSHallEffectSensorCapability,
                            _kGSHardwareEncodeSnapshotsCapability,
                            _kGSHardwareKeyboardCapability,
                            _kGSHardwareSnapshotsRequirePurpleGfxCapability,
                            _kGSHasAllFeaturesCapability,
                            _kGSHearingAidAudioEqualizationCapability,
                            _kGSHearingAidLowEnergyAudioCapability,
                            _kGSHearingAidPowerReductionCapability,
                            _kGSHiDPICapability, _kGSHiccoughInterval,
                            _kGSHideNonDefaultApplicationsCapability,
                            _kGSIOSurfaceBackedImagesCapability,
                            _kGSInternationalSettingsCapability,
                            _kGSLTEDeviceCapability, _kGSLaunchModeCapability,
                            _kGSLaunchModePostAnimate, _kGSLaunchModePreAnimate,
                            _kGSLaunchModeSerial,
                            _kGSLoadThumbnailsWhileScrollingCapability,
                            _kGSLocalizedDeviceNameString,
                            _kGSLocationRemindersCapability,
                            _kGSLocationServicesCapability, _kGSMMSCapability,
                            _kGSMagnetometerCapability, _kGSMainScreenHeight,
                            _kGSMainScreenOrientation, _kGSMainScreenScale,
                            _kGSMainScreenWidth, _kGSMarketingNameString,
                            _kGSMicrophoneCapability, _kGSMultitaskingCapability,
                            _kGSMultitaskingGesturesCapability,
                            _kGSNikeIpodCapability,
                            _kGSNotGreenTeaDeviceCapability,
                            _kGSOpenGLES1Capability, _kGSOpenGLES2Capability,
                            _kGSPTPLargeFilesCapability, _kGSPeer2PeerCapability,
                            _kGSPersonalHotspotCapability,
                            _kGSPhotoAdjustmentsCapability,
                            _kGSPhotoStreamCapability,
                            _kGSPiezoClickerCapability,
                            _kGSPlatformStandAloneContactsCapability,
                            _kGSProximitySensorCapability,
                            _kGSRearFacingCameraCapability,
                            _kGSRingerSwitchCapability, _kGSSMSCapability,
                            _kGSScreenDimensionsCapability,
                            _kGSSensitiveUICapability, _kGSShoeboxCapability,
                            _kGSSiriGestureCapability, _kGSSoftwareDimmingAlpha,
                            _kGSSystemTelephonyOfAnyKindCapability,
                            _kGSTVOutCrossfadeCapability,
                            _kGSTVOutSettingsCapability,
                            _kGSTelephonyMaximumGeneration,
                            _kGSUnifiedIPodCapability, _kGSVOIPCapability,
                            _kGSVeniceCapability, _kGSVideoCameraCapability,
                            _kGSVideoStillsCapability,
                            _kGSVoiceControlCapability,
                            _kGSVolumeButtonCapability, _kGSWAPICapability,
                            _kGSWiFiCapability, _kGSYouTubeCapability,
                            _kGSYouTubePluginCapability, _kGSiPadCapability ]
...

name: Analyse Commands

on:
  push:
    branches:
      - main

env:
  THEOS: ''
  GIT_TAG: ''
  GIT_COMMIT: ''
  XCODE_VERSION: '14.3.1'

jobs:
  build:
    name: Build and generate compilation database
    runs-on: macos-13

    steps:
      - name: Setup Xcode version
        uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: ${{ env.XCODE_VERSION }}

      - name: Install Homebrew dependencies
        run: |
          HOMEBREW_NO_AUTO_UPDATE=1 HOMEBREW_NO_INSTALLED_DEPENDENTS_CHECK=1 brew install dpkg make libplist openssl@3
          echo "/usr/local/opt/make/libexec/gnubin" >> $GITHUB_PATH

      - name: Checkout ldid
        uses: actions/checkout@v4
        with:
          repository: Lessica/ldid
          ref: master
          path: ldid

      - name: Build ldid
        run: |
          cd $GITHUB_WORKSPACE/ldid
          make install

      - name: Checkout XXTouchNG/theos
        uses: actions/checkout@v4
        with:
          repository: XXTouchNG/theos
          ref: 78ee784d8d3238982c9abdc58cd39919263648b1
          path: theos
          submodules: recursive

      - name: Add THEOS environment variables
        run: |
          rm -rf $GITHUB_WORKSPACE/theos/sdks
          echo "THEOS=$GITHUB_WORKSPACE/theos" >> $GITHUB_ENV

      - name: Checkout theos/sdks
        uses: actions/checkout@v4
        with:
          repository: theos/sdks
          ref: master
          path: ${{ env.THEOS }}/sdks

      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: Lessica/TrollSpeed
          ref: main
          path: TrollSpeed
          submodules: recursive

      - name: Setup build environment
        run: |
          echo "Available SDKs: $(find $THEOS/sdks -name "*.sdk" -maxdepth 1 -print)"
          echo "FINALPACKAGE=1" >> $GITHUB_ENV
          cd $GITHUB_WORKSPACE/TrollSpeed
          git fetch --tags
          echo "GIT_TAG=$(git describe --tags --always --abbrev=0)" >> $GITHUB_ENV

      - name: Build compilation database
        run: |
          cd $GITHUB_WORKSPACE/TrollSpeed
          ./gen-control.sh ${{ env.GIT_TAG }}
          THEOS_PACKAGE_SCHEME=rootless make commands
          make clean
          make commands

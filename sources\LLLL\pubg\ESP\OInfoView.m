
#import "OInfoView.h"
#import "Weapon.h"

@interface OInfoView()

@end

@implementation OInfoView

-(instancetype)initWithFrame:(CGRect)frame
{
    self=[super initWithFrame:frame];
    if (self) {
        _disLb = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, 100, 10)];
        _disLb.textAlignment=NSTextAlignmentCenter;
        _disLb.textColor = [UIColor whiteColor];
        _disLb.font = [UIFont boldSystemFontOfSize:9];
        _disLb.backgroundColor = [UIColor clearColor];
        [self addSubview:_disLb];
        
        _infoBar = [[UIView alloc] initWithFrame:CGRectMake(0, 10, 100, 15)];
        _infoBar.backgroundColor = [UIColor clearColor];
        _infoBar.layer.masksToBounds=YES;
        _infoBar.backgroundColor = [UIColor colorWithRed:0.5 green:0.5 blue:0.5 alpha:0.6];
        [self addSubview:_infoBar];
        
        _teamLb = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, 20, 15)];
        _teamLb.textAlignment=NSTextAlignmentCenter;
        _teamLb.textColor = [UIColor whiteColor];
        _teamLb.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.4];
        _teamLb.font = [UIFont boldSystemFontOfSize:9];
        [_infoBar addSubview:_teamLb];
        
        _nameLb = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, 100, -7)];
        _nameLb.textAlignment=NSTextAlignmentCenter;
        _nameLb.textColor = [UIColor whiteColor];
        _nameLb.backgroundColor = [UIColor clearColor];
        _nameLb.font = [UIFont boldSystemFontOfSize:8];
        [self addSubview:_nameLb];
        
        // 箭头
        _arrowLb = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, 100, 27)];
        _arrowLb.textAlignment=NSTextAlignmentCenter;
        _arrowLb.textColor = [UIColor whiteColor];
        _arrowLb.font = [UIFont boldSystemFontOfSize:9];
        _arrowLb.backgroundColor = [UIColor clearColor];
        [self addSubview:_arrowLb];
        
        
        _xueBar = [[UIView alloc] initWithFrame:CGRectMake(0, 25, 100, 2)];
        _xueBar.backgroundColor = [UIColor colorWithRed:1 green:1 blue:1 alpha:0.6];
        //_xueBar.backgroundColor = [UIColor colorWithRed:1 green:1 blue:1 alpha:0];
        [self addSubview:_xueBar];
        
        _weapon = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, 55, 45)];
        _weapon.center = CGPointMake(50,-12);
        _weapon.backgroundColor = [UIColor clearColor];
        _weapon.contentMode = UIViewContentModeCenter;
        [self addSubview:_weapon];
        
        _delta = [CAShapeLayer layer];
        _delta.strokeColor = [UIColor clearColor].CGColor;
        _delta.fillColor = [UIColor colorWithRed:0.5 green:0.5 blue:0.5 alpha:0.6].CGColor;
        UIBezierPath *delb = [UIBezierPath bezierPath];
        [delb moveToPoint:CGPointMake(45, 27)];
        [delb addLineToPoint:CGPointMake(50, 32)];
        [delb addLineToPoint:CGPointMake(55, 27)];
        [delb closePath];
        _delta.path = delb.CGPath;
        [self.layer addSublayer:_delta];
    }
    return self;
}

#pragma mark--功能性方法
-(UIColor*)colorWithHex:(NSUInteger)hex
                  alpha:(CGFloat)alpha
{
    float r, g, b, a;
    a = alpha;
    b = hex & 0x0000FF;
    hex = hex >> 8;
    g = hex & 0x0000FF;
    hex = hex >> 8;
    r = hex;

    return [UIColor colorWithRed:r/255.0f
                           green:g/255.0f
                           blue:b/255.0f
                           alpha:a];
}

#pragma mark--给属性重新赋值
-(void)setDis:(CGFloat)dis
{
    _disLb.text=[NSString stringWithFormat:@"[%d]",(int)dis];
}

-(void)setDisColor:(UIColor*) color
{
    _disLb.textColor = color;
}

-(void)setName:(NSString *)name
{
    _nameLb.text=[NSString stringWithFormat:@"%@",name];
}

-(void)setNameColor:(UIColor*) color
{
    _nameLb.textColor = color;
}

-(void)setTeam:(int)team
{
    if(team==-1)
    {
        _infoBar.backgroundColor = [UIColor clearColor];
        _delta.fillColor = [UIColor clearColor].CGColor;
        _teamLb.text=@"";
    }
    else{
        //NSUInteger colorArray[] = {0x00BFFF};//绘图背景颜色
            NSUInteger colorArray[] = {0x333333,0x333366,0x336633,0x336633,0x339999,0x33CCCC,0x663333,0x663366,0x663366,0x999933,0xCC3366,0xCC6666,0xCC6633,0xFF9966,0x660066};
        UIColor *color = [self colorWithHex:colorArray[team % (sizeof(colorArray)/8)] alpha:0.6];
        _infoBar.backgroundColor = color;
        _delta.fillColor = color.CGColor;
        
        _teamLb.text=[NSString stringWithFormat:@"%d",team];
    }
}

-(void)setPoint:(CGPoint)point
{
    CGPoint centerPoint = CGPointMake(UIScreen.mainScreen.bounds.size.width * 0.5, UIScreen.mainScreen.bounds.size.height * 0.5);
    float xDis,yDis,centerDis;
    xDis = point.x + 50 - centerPoint.x;
    yDis = point.y + 32 - centerPoint.y;
    centerDis = sqrt(xDis*xDis+yDis*yDis);
    if(centerDis <= 50) {
        _disLb.hidden = YES;
        _infoBar.hidden = YES;
    } else {
        _disLb.hidden = NO;
        _infoBar.hidden = NO;
    }
    self.center=point;
}

-(void)setXue:(CGFloat)xue
{
    if(xue > 100) xue = 100;
    _xueBar.frame = CGRectMake(0, 25, xue, 2);
}

-(void)setAi:(Boolean)ai
{
    if(ai) {
        _xueBar.backgroundColor = [self colorWithHex:0x19CAAD alpha:0.6];
    } else {
        _xueBar.backgroundColor = [UIColor colorWithRed:1 green:1 blue:1 alpha:0.6];
    }
}
-(void)setWeaponID:(int)WeaponID
{
    if(WeaponID==0){_weapon.image = NULL;}
    else if(WeaponID==666){_weapon.image = NULL;}
    else if(WeaponID==101001){_weapon.image = [Weapon SharedFuckData].AKM;}
    else if(WeaponID==101002){_weapon.image = [Weapon SharedFuckData].M16A4;}
    else if(WeaponID==101003){_weapon.image = [Weapon SharedFuckData].Scar;}
    else if(WeaponID==101004){_weapon.image = [Weapon SharedFuckData].M416;}
    else if(WeaponID==101005){_weapon.image = [Weapon SharedFuckData].Groza;}
    else if(WeaponID==101006){_weapon.image = [Weapon SharedFuckData].AUG;}
    else if(WeaponID==101007){_weapon.image = [Weapon SharedFuckData].QBZ;}
    else if(WeaponID==101008){_weapon.image = [Weapon SharedFuckData].M762;}
    else if(WeaponID==101009){_weapon.image = [Weapon SharedFuckData].MK47;}
    else if(WeaponID==101010){_weapon.image = [Weapon SharedFuckData].G36C;}
    else if(WeaponID==101011){_weapon.image = [Weapon SharedFuckData].ACVAL;}
    else if(WeaponID==101012){_weapon.image = [Weapon SharedFuckData].mihuan;}
    else if(WeaponID==102001){_weapon.image = [Weapon SharedFuckData].UZI;}
    else if(WeaponID==102002){_weapon.image = [Weapon SharedFuckData].Ump45;}
    else if(WeaponID==102003){_weapon.image = [Weapon SharedFuckData].Vector;}
    else if(WeaponID==102004){_weapon.image = [Weapon SharedFuckData].tomos;}
    else if(WeaponID==102005){_weapon.image = [Weapon SharedFuckData].PP19;}
    else if(WeaponID==102007){_weapon.image = [Weapon SharedFuckData].MP5K;}
    else if(WeaponID==102105){_weapon.image = [Weapon SharedFuckData].P90;}
    else if(WeaponID==103001){_weapon.image = [Weapon SharedFuckData].Kar98k;}
    else if(WeaponID==103002){_weapon.image = [Weapon SharedFuckData].M24;}
    else if(WeaponID==103003){_weapon.image = [Weapon SharedFuckData].AWM;}
    else if(WeaponID==103004){_weapon.image = [Weapon SharedFuckData].SKS;}
    else if(WeaponID==103005){_weapon.image = [Weapon SharedFuckData].VSS;}
    else if(WeaponID==103006){_weapon.image = [Weapon SharedFuckData].Mini14;}
    else if(WeaponID==103007){_weapon.image = [Weapon SharedFuckData].MK14;}
    else if(WeaponID==103008){_weapon.image = [Weapon SharedFuckData].Win94;}
    else if(WeaponID==103009){_weapon.image = [Weapon SharedFuckData].SLR;}
    else if(WeaponID==103010){_weapon.image = [Weapon SharedFuckData].QBU;}
    else if(WeaponID==103011){_weapon.image = [Weapon SharedFuckData].moxinnagan;}//莫辛纳甘
    else if(WeaponID==103012){_weapon.image = [Weapon SharedFuckData].AMR;}
    else if(WeaponID==103013){_weapon.image = [Weapon SharedFuckData].M417;}
    else if(WeaponID==103014){_weapon.image = [Weapon SharedFuckData].MK20;}
    //else if(WeaponID==103100){_weapon.image = [Weapon SharedFuckData].MK12;}
    else if(WeaponID==104001){_weapon.image = [Weapon SharedFuckData].S686;}
    else if(WeaponID==104002){_weapon.image = [Weapon SharedFuckData].S1897;}
    else if(WeaponID==104003){_weapon.image = [Weapon SharedFuckData].S12K;}
    else if(WeaponID==104004){_weapon.image = [Weapon SharedFuckData].DBS;}
    else if(WeaponID==104100){_weapon.image = [Weapon SharedFuckData].SPAS;}
    else if(WeaponID==105001){_weapon.image = [Weapon SharedFuckData].M249;}
    else if(WeaponID==105002){_weapon.image = [Weapon SharedFuckData].DP28;}
    else if(WeaponID==105010){_weapon.image = [Weapon SharedFuckData].MG3;}
    else if(WeaponID==107001){_weapon.image = [Weapon SharedFuckData].BOW;}
    else if(WeaponID==107007){_weapon.image = [Weapon SharedFuckData].BurnBOW;}
    else if(WeaponID==602004){_weapon.image = [Weapon SharedFuckData].Burn;}
    else if(WeaponID==108001||WeaponID==108002||WeaponID==108003||WeaponID==108004){_weapon.image = [Weapon SharedFuckData].Pan;}
    else if(WeaponID==106005||WeaponID==106006||WeaponID==106008||WeaponID==106010){_weapon.image = [Weapon SharedFuckData].handgun;}
    else{_weapon.image = NULL;}
}
@end

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>StringsTable</key>
	<string>Root</string>
	<key>PreferenceSpecifiers</key>
	<array>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Custom Font Size</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Uses Custom Font Size</string>
			<key>Key</key>
			<string>usesCustomFontSize</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSSliderSpecifier</string>
			<key>Title</key>
			<string>Custom Font Size</string>
			<key>Key</key>
			<string>realCustomFontSize</string>
			<key>DefaultValue</key>
			<real>9</real>
			<key>MinimumValue</key>
			<real>8</real>
			<key>MaximumValue</key>
			<real>12</real>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Custom Offset</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Uses Custom Offset</string>
			<key>Key</key>
			<string>usesCustomOffset</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSSliderSpecifier</string>
			<key>Title</key>
			<string>Custom Offset X</string>
			<key>Key</key>
			<string>realCustomOffsetX</string>
			<key>DefaultValue</key>
			<real>0</real>
			<key>MinimumValue</key>
			<real>-100</real>
			<key>MaximumValue</key>
			<real>100</real>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSSliderSpecifier</string>
			<key>Title</key>
			<string>Custom Offset Y</string>
			<key>Key</key>
			<string>realCustomOffsetY</string>
			<key>DefaultValue</key>
			<real>0</real>
			<key>MinimumValue</key>
			<real>-100</real>
			<key>MaximumValue</key>
			<real>100</real>
		</dict>
	</array>
</dict>
</plist>

//情情 2123837096
#import <Foundation/Foundation.h>
#import "ReadData.h"
#import "MemoryTool.h"
#import "Offset.h"
#import <UIKit/UIKit.h>
#import "UdpDataManager.h"
#import "utf.h"
#import "function.h"
#import "GetTask.h"

#include <pthread.h>

#define SCREEN_WIDTH [UIScreen mainScreen].bounds.size.width>[UIScreen mainScreen].bounds.size.height?[UIScreen mainScreen].bounds.size.width:[UIScreen mainScreen].bounds.size.height
#define SCREEN_HEIGHT [UIScreen mainScreen].bounds.size.width<[UIScreen mainScreen].bounds.size.height?[UIScreen mainScreen].bounds.size.width:[UIScreen mainScreen].bounds.size.height

#define kWidth  [UIScreen mainScreen].bounds.size.width>[UIScreen mainScreen].bounds.size.height?[UIScreen mainScreen].bounds.size.width:[UIScreen mainScreen].bounds.size.height
#define kHeight [UIScreen mainScreen].bounds.size.width<[UIScreen mainScreen].bounds.size.height?[UIScreen mainScreen].bounds.size.width:[UIScreen mainScreen].bounds.size.height
#define __fastcall
#pragma mark 全局变量-----------------------------------------------------------------------------
float screen_width,screen_height,halfsw,halfsh,fmpi;
mach_port_t task;
long base_addr;
long GWorld;
long IDLineOfSight;
long localPlayerController;
long TNameEntryArray;
long MySelf;
int MyTeam;
MinimalViewInfo POV;
#pragma mark 函数start------------------------------------------------------------------------------
bool lineTraceTargetActor(long Actor,Vector3 viewPoint)
{
    int LineTraceData[100] = {0};
    Vector3 StartPoint = POV.Location;
    long Hit = base_addr+0x10B8A8919;
    
    //未修改读内存
    reinterpret_cast<void(*)(long, long, long, long, long)> (base_addr+0x106AF78A0)((long)&LineTraceData[0], IDLineOfSight, (long)&LineTraceData[40], 1, MySelf);
    reinterpret_cast<void(*)(long, long)> (base_addr+0x106AFB388)((long)&LineTraceData[0], Actor);
    int ret = reinterpret_cast<int(*)(long, Vector3*, Vector3*, long, long, long)>(base_addr+0x106B06FC4)(GWorld, &StartPoint, &viewPoint, 3, (long)&LineTraceData[0], Hit);
    
    return (ret & 0x1) == 0;
}
Vector2 WorldToScreen(Vector3 worldLocation) {
    FMatrix tempMatrix = RotatorToMatrix(POV.Rotation);
    Vector3 vAxisX(tempMatrix[0][0], tempMatrix[0][1], tempMatrix[0][2]);
    Vector3 vAxisY(tempMatrix[1][0], tempMatrix[1][1], tempMatrix[1][2]);
    Vector3 vAxisZ(tempMatrix[2][0], tempMatrix[2][1], tempMatrix[2][2]);
    Vector3 vDelta = worldLocation - POV.Location;
    Vector3 vTransformed(Vector3::Dot(vDelta, vAxisY), Vector3::Dot(vDelta, vAxisZ), Vector3::Dot(vDelta, vAxisX));
    if (vTransformed.Z < 1.0f) {
        vTransformed.Z = 1.0f;
    }
    return Vector2(
               (halfsw + vTransformed.X * (halfsw / tanf(POV.FOV * fmpi)) / vTransformed.Z),
               (halfsh - vTransformed.Y * (halfsw / tanf(POV.FOV * fmpi)) / vTransformed.Z)
           );
}
void (*AddControllerYawInput)(void *actot, float val);
void (*AddControllerRollInput)(void *actot, float val);
void (*AddControllerPitchInput)(void *actot, float val);
#pragma mark 函数end--------------------------------------------------------------------------------
@interface ReadData ()
{
    long playerCameraManager;
    
    UdpDataManager *udp;
    
    Vector2 SCREEN_Line;
    
    bool isFire;
    bool isADS;
    long ShootWeaponEntityComp;
    long bastaimactoraddr;
    
    float TempRecoil;
    float AccessoriesVRecoilFactor;
    float RecoilKickADS;
    
    bool FireAuto;
    int tempdis;
}

@property (nonatomic, assign) UInt32 shouleiID;
@property (nonatomic, assign) UInt32 sanjitouID;
@property (nonatomic, assign) UInt32 sanjijiaID;
@property (nonatomic, assign) UInt32 sanjibaoID;
@property (nonatomic, assign) UInt32 kongtouID;

@property (nonatomic, strong) NSMutableSet *PlayerID;
@property (nonatomic, strong) NSMutableSet *ID;
@property (nonatomic, strong) NSMutableSet *vehicleID;

@property (nonatomic, assign) Vector3 aimpos;

@end

static ReadData *fuckdata;
@implementation ReadData

+ (instancetype)share
{
    static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
             if (!fuckdata) {
                  fuckdata = [[ReadData alloc] init];
               }
        });
         return fuckdata;
}
- (instancetype)init
{
    NSLog(@"[情情]:%s:=======", __func__);
    self = [super init];
    if (self) {
        udp = [UdpDataManager Share];
        _PlayerID = [[NSMutableSet alloc]init];
        _ID = [[NSMutableSet alloc]init];
        _vehicleID = [[NSMutableSet alloc]init];
        _GreenLayer = [UIBezierPath bezierPath];
        _YellowLayer = [UIBezierPath bezierPath];
        _WhiteLayer = [UIBezierPath bezierPath];
        _RedLayer = [UIBezierPath bezierPath];
        SCREEN_Line.X = SCREEN_WIDTH/2;
        SCREEN_Line.Y = 45;
        task = get_game_task();
        base_addr = get_base_address();
        NSLog(@"[情情]:%s:task:%u:base_addr:%lu", __func__, task, base_addr);
        //IDLineOfSight = *(long*)(base_addr + 0x10B5E1F28);
        ReadMemoryByTask(task, base_addr + 0x10B5E1F28, &IDLineOfSight, sizeof(IDLineOfSight));
        
        screen_width = SCREEN_WIDTH;
        screen_height = SCREEN_HEIGHT;
        halfsw = screen_width/2;
        halfsh = screen_height/2;
        fmpi = (float)(M_PI/360);
        
        pthread_t AddControllerThread;
        pthread_create(&AddControllerThread, nullptr, AddController, nullptr);
    }
    return self;
    }
    void *AddController(void *) {
        while (!AddControllerYawInput) {
            if(IsValidAddress(MySelf)){
                unsigned long VirtualFunctionTable;// = *(long*)MySelf;
                ReadMemoryByTask(task, MySelf, &VirtualFunctionTable, sizeof(VirtualFunctionTable));
                long v1;
                long v2;
                long v3;
                ReadMemoryByTask(task, VirtualFunctionTable + 0xA10, &v1, sizeof(v1));
                AddControllerYawInput = (void (*)(void *, float)) v1;
                //AddControllerYawInput = (void (*)(void *, float)) (*(long*)(VirtualFunctionTable + 0xA10));//0xA10
                
                ReadMemoryByTask(task, VirtualFunctionTable + 0xA18, &v2, sizeof(v2));
                AddControllerRollInput = (void (*)(void *, float)) v2;
                //AddControllerRollInput = (void (*)(void *, float)) (*(long*)(VirtualFunctionTable + 0xA18));//
                
                ReadMemoryByTask(task, VirtualFunctionTable + 0xA08, &v3, sizeof(v3));
                AddControllerPitchInput = (void (*)(void *, float)) v3;
                //AddControllerPitchInput = (void (*)(void *, float)) (*(long*)(VirtualFunctionTable + 0xA08));
                            }
            sleep(2);
        }
    return nullptr;
}
-(std::string)GetVehicleFName:(UInt32)FNameID caches:(long)TNameEntryArray
{
    std::string name = [self GetFName:FNameID caches:TNameEntryArray];
    if(isContain(name, "Scooter"))return "小绵羊";
    if(isContain(name, "Motorcycle"))return "摩托";
    if(isContain(name, "BP_VH_Tuk_"))return "太君摩托";
    if(isContain(name, "BP_VH_Buggy_"))return "蹦蹦";
    if(isContain(name, "PickUp_0"))return "皮卡";
    if(isContain(name, "Mirado_"))return "跑车";
    if(isContain(name, "VH_Dacia_"))return "轿车";
    if(isContain(name, "AquaRail_"))return "摩托艇";
    if(isContain(name, "BP_VH_CoupeRB_"))return "轿跑";
    if(isContain(name, "CoupeRB_1"))return "轿跑";
    if(isContain(name, "VH_MiniBus_"))return "迷你巴士";
    if(isContain(name, "VH_BRDM_"))return "装甲车";
    if(isContain(name, "VH_UAZ"))return "吉普";
    if(isContain(name, "VH_Mountainbike_Training_C"))return "自行车";
    if(isContain(name, "PickUp_BP_Mountainbike1_C"))return "自行车";
    if(isContain(name, "Skill_Spawn_Mountaibike_C"))return "自行车";
    if(isContain(name, "VH_Mountainbike_C"))return "自行车";
    if(isContain(name, "Snowmobile"))return "雪橇";
    if(isContain(name, "Snowbike"))return "自行车";
    if(isContain(name, "PG117"))return "快艇";
    return "载具";
}
-(std::string)GetFName:(UInt32)FNameID caches:(long)TNameEntryArray
{
    long FNameEntryArr;// = ReadPtr(TNameEntryArray + ((FNameID / 0x4000) * 8));
    ReadMemoryByTask(task, TNameEntryArray + ((FNameID / 0x4000) * 8), &FNameEntryArr, sizeof(FNameEntryArr));
    long FNameEntry;// = ReadPtr(FNameEntryArr + ((FNameID % 0x4000) * 8));
    ReadMemoryByTask(task, FNameEntryArr + ((FNameID % 0x4000) * 8), &FNameEntry, sizeof(FNameEntry));
    if(!IsValidAddress(FNameEntry))return "";
    std::string name(100, '\0');
    memcpy((void *) name.data(), reinterpret_cast<void *>(FNameEntry + 0xE), 100 * sizeof(char));
    name.shrink_to_fit();
    return name;
}
-(void)ReturnData:(returndata)block
{
    if(!udp.huizhi)return;
    NSLog(@"[情情]:%s:读取数据", __func__);
    NSLog(@"[情情]:%s:base_addr:%lu", __func__, base_addr);
    //GWorld = *(long*)(base_addr + Offsets::UWorld);
    ReadMemoryByTask(task, base_addr + Offsets::UWorld, &GWorld, sizeof(GWorld));
    NSLog(@"[情情]:%s:GWorld:%lu", __func__, GWorld);
    if(!IsValidAddress(GWorld))return;
    
    long NetDriver;// = *(long*)(GWorld + Offsets::NetDriver);
    ReadMemoryByTask(task, GWorld + Offsets::NetDriver, &NetDriver, sizeof(NetDriver));
    NSLog(@"[情情]:%s:NetDriver:%lu", __func__, NetDriver);
    
    if(!IsValidAddress(NetDriver))return;
    long ServerConnection;// = *(long*)(NetDriver + Offsets::ServerConnection);
    ReadMemoryByTask(task, NetDriver + Offsets::ServerConnection, &ServerConnection, sizeof(ServerConnection));
    
    //localPlayerController = *(long*)(ServerConnection + Offsets::PlayerController);
    ReadMemoryByTask(task, ServerConnection + Offsets::PlayerController, &localPlayerController, sizeof(localPlayerController));
    
    if(!IsValidAddress(localPlayerController))return;
    
    //MySelf = *(long*)(localPlayerController + Offsets::MySelf);
    ReadMemoryByTask(task, localPlayerController + Offsets::MySelf, &MySelf, sizeof(MySelf));
    if(IsValidAddress(MySelf)){
        int TeamID;// = *(int*)(MySelf + Offsets::TeamID);
        ReadMemoryByTask(task, MySelf + Offsets::TeamID, &TeamID, sizeof(TeamID));
        if(TeamID<101)MyTeam = TeamID;
    }
    
    //playerCameraManager = *(long*)(localPlayerController + Offsets::playerCameraManager);
    ReadMemoryByTask(task, localPlayerController + Offsets::playerCameraManager, &playerCameraManager, sizeof(playerCameraManager));
    NSLog(@"[情情]:%s:playerCameraManager:%lu", __func__, playerCameraManager);
    if(!IsValidAddress(playerCameraManager))return;
    
    //POV = *(MinimalViewInfo*)(playerCameraManager + Offsets::ViewTarget + Offsets::MinimalViewInfo);
    ReadMemoryByTask(task, playerCameraManager + Offsets::ViewTarget + Offsets::MinimalViewInfo, &POV, sizeof(POV));
    ReadMemoryByTask(task, playerCameraManager + Offsets::ViewTarget + Offsets::MinimalViewInfo + 0x30, &POV.FOV, sizeof(POV.FOV));
//    ReadMemoryByTask(task, playerCameraManager + Offsets::ViewTarget + Offsets::MinimalViewInfo + 0x30, &POV.FOV, sizeof(POV.FOV)); // 需要重新调整POV.FOV才能正常绘制，POV结构体异常问题，参考h5 camViewInfo结构体
    NSMutableArray *temparr = @[].mutableCopy;
    ActorInfo alldata;
    float minCrossCenter = 100000;
    long ULevel;// = *(long*)(GWorld + Offsets::ULevel);
    ReadMemoryByTask(task, GWorld + Offsets::ULevel, &ULevel, sizeof(ULevel));
    long ActorArray;// = *(long*)(ULevel + Offsets::ActorArray);
    ReadMemoryByTask(task, ULevel + Offsets::ActorArray, &ActorArray, sizeof(ActorArray));
    int ActorCount;// = *(int*)(ULevel + Offsets::ActorCount);
    ReadMemoryByTask(task, ULevel + Offsets::ActorCount, &ActorCount, sizeof(ActorCount));
    
    for (int i = 0; i < ActorCount; ++i) {
        long Actor;// = *(long*)(ActorArray + i * 8);
        ReadMemoryByTask(task, ActorArray + i * 8, &Actor, sizeof(Actor));
        NSLog(@"[情情]:%s:Actor:%ld", __func__, Actor);
        if (!IsValidAddress(Actor) || Actor == MySelf) continue;
        
        Byte bDead;// = *(Byte*)(Actor + Offsets::bDead);
        ReadMemoryByTask(task, Actor + Offsets::bDead, &bDead, sizeof(bDead));
        NSLog(@"[情情]:%s:bDead:%hhu", __func__, bDead);
        
        if(bDead!=2)continue;
        alldata.type = 0;
        int Team;// = *(int*)(Actor + Offsets::TeamID);
        ReadMemoryByTask(task, Actor + Offsets::TeamID, &Team, sizeof(Team));
        NSLog(@"[情情]:%s:Team:%d", __func__, Team);
        
        NSLog(@"[情情]:%s:MyTeam == Team:%d", __func__, MyTeam == Team);
        if (MyTeam == Team) continue;
        if (Team == -1) continue; // 排除小黄鸡
        
        float HpMax;// = *(float*)(Actor + Offsets::Health);
        ReadMemoryByTask(task, Actor + Offsets::情情, &HpMax, sizeof(HpMax));
        if (HpMax != 100 && HpMax != 110 && HpMax != 120 &&
            HpMax != 130 && HpMax != 140 && HpMax != 150 &&
            HpMax != 160 && HpMax != 170 && HpMax != 180 &&
            HpMax != 190 && HpMax != 200) {
            continue;
        } // h5抄过来
        
        float Hp;// = *(float*)(Actor + Offsets::Health);
        ReadMemoryByTask(task, Actor + Offsets::QQ2123837096, &Hp, sizeof(Hp));
        NSLog(@"[情情]:%s:Hp:%f", __func__, Hp);
        
        bool IsAi;// = *(bool*)(Actor + Offsets::bIsAI);
        ReadMemoryByTask(task, Actor + Offsets::bIsAI, &IsAi, sizeof(IsAi));
        NSLog(@"[情情]:%s:IsAi:%d", __func__, IsAi);
        
        NSLog(@"[情情]:[name]:%s:name:111111", __func__);
        if(!IsAi){
            NSLog(@"[情情]:[name]:%s:name:22222", __func__);
            alldata.Team = Team;
            if(udp.bname){
                NSLog(@"[情情]:[name]:%s:name:33333", __func__);
                long nameAddr;// = *(long*)(Actor + Offsets::PlayerName);
                ReadMemoryByTask(task, Actor + Offsets::PlayerName, &nameAddr, sizeof(nameAddr));
                
                NSLog(@"[情情]:[name]:%s:name:44444", __func__);
                if (IsValidAddress(nameAddr)) {
                    NSLog(@"[情情]:[name]:%s:name:55555", __func__);
                    UTF8 name[32] = "";
                    UTF16 buf16[16] = {0};
//                    memcpy(buf16, reinterpret_cast<void *>(nameAddr), 28);
                    NSLog(@"[情情]:[name]:%s:name:66666", __func__);
                    if (!ReadMemoryByTask(task, nameAddr, buf16, 28)) alldata.name = "";
                    else{
                        NSLog(@"[情情]:[name]:%s:name:77777", __func__);
                        Utf16_To_Utf8(buf16, name, 28, strictConversion);
                        alldata.name = name;
                    }
                    NSLog(@"[情情]:[name]:%s:name:88888", __func__);
                }else{
                    alldata.name = "";
                }
                NSLog(@"[情情]:[name]:%s:name:%s", __func__, alldata.name.c_str());
            }
        } else {
            NSLog(@"[情情]:[name]:%s:name:99999", __func__);
            if(udp.bname){
                NSLog(@"[情情]:[name]:%s:name:00000", __func__);
                alldata.name = "人机";
            }else {
                NSLog(@"[情情]:[name]:%s:name:01111", __func__);
                alldata.name = "";
            }
            alldata.Team = 0;
            NSLog(@"[情情]:[name]:%s:name:%s", __func__, alldata.name.c_str());
        }
        long rootComponent;// = *(long*)(Actor + Offsets::rootComponent);
        ReadMemoryByTask(task, Actor + Offsets::rootComponent, &rootComponent, sizeof(rootComponent));
        NSLog(@"[情情]:%s:rootComponent:%ld", __func__, rootComponent);
        
        if (!IsValidAddress(rootComponent)) continue;
        Vector3 worldPos;// = *(Vector3*)(rootComponent + Offsets::worldPos);
        ReadMemoryByTask(task, rootComponent + Offsets::worldPos, &worldPos, sizeof(worldPos));
        
        int distance = Vector3::Distance(worldPos, POV.Location) / 100.0f;
        if(distance>400)continue;
        
        Vector2 XY = WorldToScreen(Vector3(worldPos.X, worldPos.Y, worldPos.Z+63), POV, screen_width, screen_height);
        XY.Y -= 40;
        alldata.X = XY.X;
        alldata.Y = XY.Y;

        long mesh;// = *(long*)(Actor + Offsets::meshAddr);
        ReadMemoryByTask(task, Actor + Offsets::meshAddr, &mesh, sizeof(mesh));
        NSLog(@"[情情]:%s:mesh:%ld", __func__, mesh);
        
        if (!IsValidAddress(mesh))continue;
        
        FTransform meshTrans;// = *(FTransform*)(mesh + 0x1a0);
        ReadMemoryByTask(task, mesh + 0x1a0, &meshTrans, sizeof(meshTrans));
        // float FreeFall_MaxRightExtraFallSpeed
        
        FMatrix c2wMatrix = TransformToMatrix(meshTrans);
        long boneArray;// = *(long*)(mesh + Offsets::StaticMesh);
        ReadMemoryByTask(task, mesh + Offsets::StaticMesh, &boneArray, sizeof(boneArray));
        
        if (!IsValidAddress(boneArray))continue;
        
        BoneVisibleData Visible;
        Vector3 head = getBoneWorldPos(boneArray + 6 * 48,c2wMatrix,task);
//        Visible.head = lineTraceTargetActor(Actor,head);
        Visible.head = 1;
        Vector3 chest = getBoneWorldPos(boneArray + 4 * 48,c2wMatrix,task);
//        Visible.chest = lineTraceTargetActor(Actor,chest);
        Visible.chest = 1;
        Vector3 leftShoulder = getBoneWorldPos(boneArray + 12 * 48,c2wMatrix,task);
//        Visible.leftShoulder = lineTraceTargetActor(Actor,leftShoulder);
        Visible.leftShoulder = 1;
        Vector3 rightShoulder = getBoneWorldPos(boneArray + 33 * 48,c2wMatrix,task);
//        Visible.rightShoulder = lineTraceTargetActor(Actor,rightShoulder);
        Visible.rightShoulder = 1;
        Vector3 leftElbow = getBoneWorldPos(boneArray + 13 * 48,c2wMatrix,task);
//        Visible.leftElbow = lineTraceTargetActor(Actor,leftElbow);
        Visible.leftElbow = 1;
        Vector3 rightElbow = getBoneWorldPos(boneArray + 34 * 48,c2wMatrix,task);
//        Visible.rightElbow = lineTraceTargetActor(Actor,rightElbow);
        Visible.rightElbow = 1;
        Vector3 leftHand = getBoneWorldPos(boneArray + 14 * 48,c2wMatrix,task);
//        Visible.leftHand = lineTraceTargetActor(Actor,leftHand);
        Visible.leftHand = 1;
        Vector3 rightHand = getBoneWorldPos(boneArray + 35 * 48,c2wMatrix,task);
//        Visible.rightHand = lineTraceTargetActor(Actor,rightHand);
        Visible.rightHand = 1;
        Vector3 pelvis = getBoneWorldPos(boneArray + 1 * 48,c2wMatrix,task);
//        Visible.pelvis = lineTraceTargetActor(Actor,pelvis);
        Visible.pelvis = 1;
        Vector3 leftThigh = getBoneWorldPos(boneArray + 56 * 48,c2wMatrix,task); // ???
//        Visible.leftThigh = lineTraceTargetActor(Actor,leftThigh);
        Visible.leftThigh = 1;
        Vector3 rightThigh = getBoneWorldPos(boneArray + 60 * 48,c2wMatrix,task); // ???
//        Visible.rightThigh = lineTraceTargetActor(Actor,rightThigh);
//        Visible.rightThigh = 1;
        Vector3 leftKnee = getBoneWorldPos(boneArray + 57 * 48,c2wMatrix,task);
//        Visible.leftKnee = lineTraceTargetActor(Actor,leftKnee);
        Visible.leftKnee = 1;
        Vector3 rightKnee = getBoneWorldPos(boneArray + 61 * 48,c2wMatrix,task);
//        Visible.rightKnee = lineTraceTargetActor(Actor,rightKnee);
        Visible.rightKnee = 1;
        Vector3 leftFoot = getBoneWorldPos(boneArray + 58 * 48,c2wMatrix,task);
//        Visible.leftFoot = lineTraceTargetActor(Actor,leftFoot);
        Visible.leftFoot = 1;
        Vector3 rightFoot = getBoneWorldPos(boneArray + 62 * 48,c2wMatrix,task);
//        Visible.rightFoot = lineTraceTargetActor(Actor,rightFoot);
        Visible.rightFoot = 1;
        
        if (udp.shexian && XY.Y > 0 && XY.Y < screen_height){ // 射线，动态超出身后不绘制
            if(!IsAi){
                [self DrawWhiteLine:SCREEN_Line end:XY];
            } else {
                [self DrawLine:SCREEN_Line end:XY visible:BoneVisible(Visible)];
            }
        }
        
        if (udp.guge && distance < 40  && XY.X > 0 && XY.X < screen_width &&
            XY.Y > 0 && XY.Y < screen_height) { // 上半身骨骼，动态超出屏幕不绘制
            if(!IsAi){
                
                [self DrawHead:WorldToScreen(head, POV, screen_width, screen_height) color:@"white" distence:distance];
                
                [self DrawWhiteLine:WorldToScreen(head, POV, screen_width, screen_height) end:WorldToScreen(chest, POV, screen_width, screen_height)];
                
                [self DrawWhiteLine:WorldToScreen(chest, POV, screen_width, screen_height) end:WorldToScreen(leftShoulder, POV, screen_width, screen_height)];
                
                [self DrawWhiteLine:WorldToScreen(chest, POV, screen_width, screen_height) end:WorldToScreen(rightShoulder, POV, screen_width, screen_height)];
                
                [self DrawWhiteLine:WorldToScreen(leftShoulder, POV, screen_width, screen_height) end:WorldToScreen(leftElbow, POV, screen_width, screen_height)];
                
                [self DrawWhiteLine:WorldToScreen(rightShoulder, POV, screen_width, screen_height) end:WorldToScreen(rightElbow, POV, screen_width, screen_height)];
                
                [self DrawWhiteLine:WorldToScreen(leftElbow, POV, screen_width, screen_height) end:WorldToScreen(leftHand, POV, screen_width, screen_height)];
                
                [self DrawWhiteLine:WorldToScreen(rightElbow, POV, screen_width, screen_height) end:WorldToScreen(rightHand, POV, screen_width, screen_height)];
                
                [self DrawWhiteLine:WorldToScreen(chest, POV, screen_width, screen_height) end:WorldToScreen(pelvis, POV, screen_width, screen_height)];
                
                //===========下半身
                [self DrawWhiteLine:WorldToScreen(pelvis, POV, screen_width, screen_height) end:WorldToScreen(leftThigh, POV, screen_width, screen_height)];

                [self DrawWhiteLine:WorldToScreen(pelvis, POV, screen_width, screen_height) end:WorldToScreen(rightThigh, POV, screen_width, screen_height)];

                [self DrawWhiteLine:WorldToScreen(leftThigh, POV, screen_width, screen_height) end:WorldToScreen(leftKnee, POV, screen_width, screen_height)];

                [self DrawWhiteLine:WorldToScreen(rightThigh, POV, screen_width, screen_height) end:WorldToScreen(rightKnee, POV, screen_width, screen_height)];

                [self DrawWhiteLine:WorldToScreen(leftKnee, POV, screen_width, screen_height) end:WorldToScreen(leftFoot, POV, screen_width, screen_height)];

                [self DrawWhiteLine:WorldToScreen(rightKnee, POV, screen_width, screen_height) end:WorldToScreen(rightFoot, POV, screen_width, screen_height)];
            } else {
                [self DrawHead:WorldToScreen(head, POV, screen_width, screen_height) color:@"red" distence:distance];
                
                [self DrawLine:WorldToScreen(head, POV, screen_width, screen_height) end:WorldToScreen(chest, POV, screen_width, screen_height) visible:0];
                
                [self DrawLine:WorldToScreen(chest, POV, screen_width, screen_height) end:WorldToScreen(leftShoulder, POV, screen_width, screen_height) visible:0];
                
                [self DrawLine:WorldToScreen(chest, POV, screen_width, screen_height) end:WorldToScreen(rightShoulder, POV, screen_width, screen_height) visible:0];
                
                [self DrawLine:WorldToScreen(leftShoulder, POV, screen_width, screen_height) end:WorldToScreen(leftElbow, POV, screen_width, screen_height) visible:0];
                
                [self DrawLine:WorldToScreen(rightShoulder, POV, screen_width, screen_height) end:WorldToScreen(rightElbow, POV, screen_width, screen_height) visible:0];
                
                [self DrawLine:WorldToScreen(leftElbow, POV, screen_width, screen_height) end:WorldToScreen(leftHand, POV, screen_width, screen_height) visible:0];
                
                [self DrawLine:WorldToScreen(rightElbow, POV, screen_width, screen_height) end:WorldToScreen(rightHand, POV, screen_width, screen_height) visible:0];
                
                [self DrawLine:WorldToScreen(chest, POV, screen_width, screen_height) end:WorldToScreen(pelvis, POV, screen_width, screen_height) visible:0];
                
                //===========下半身
                [self DrawLine:WorldToScreen(pelvis, POV, screen_width, screen_height) end:WorldToScreen(leftThigh, POV, screen_width, screen_height) visible:0];

                [self DrawLine:WorldToScreen(pelvis, POV, screen_width, screen_height) end:WorldToScreen(rightThigh, POV, screen_width, screen_height) visible:0];

                [self DrawLine:WorldToScreen(leftThigh, POV, screen_width, screen_height) end:WorldToScreen(leftKnee, POV, screen_width, screen_height) visible:0];

                [self DrawLine:WorldToScreen(rightThigh, POV, screen_width, screen_height) end:WorldToScreen(rightKnee, POV, screen_width, screen_height) visible:0];
                
                [self DrawLine:WorldToScreen(leftKnee, POV, screen_width, screen_height) end:WorldToScreen(leftFoot, POV, screen_width, screen_height) visible:0];
                
                [self DrawLine:WorldToScreen(rightKnee, POV, screen_width, screen_height) end:WorldToScreen(rightFoot, POV, screen_width, screen_height) visible:0];
            }
        }
        
        if(Hp!=0){
            if(distance<udp.zhimiaodis){
                float crossCenter = Vector2::Distance(Vector2(screen_width * 0.5,screen_height*0.5),WorldToScreen(pelvis, POV, screen_width, screen_height));
                
                if(crossCenter < udp.zhimiaoquan){
                    udp.Radius = crossCenter;
                    int point = BoneVisible(Visible);
                    if(point != 0){
                        Vector3 tempaimpos;
                        if(udp.aimpoint!=100){
                            tempaimpos = getBoneWorldPos(boneArray + udp.aimpoint * 48,c2wMatrix,task);
                        } else {
                            tempaimpos = getBoneWorldPos(boneArray + BoneVisible(Visible) * 48,c2wMatrix,task);
                        }
                        if(tempaimpos!=Vector3()){
                            if (crossCenter < minCrossCenter) {
                                minCrossCenter = crossCenter;
                                bastaimactoraddr = Actor;
                                _aimpos = tempaimpos;
                                tempdis = distance;
                            }
                        }
                    }
                }
            }
        }
//        if(udp.aimline&&_aimpos!=Vector3())
//        [self DrawRedLine:Vector2(halfsw,halfsh) end:WorldToScreen(_aimpos, POV, screen_width, screen_height)];
        int WeaponID = 0;

        if(udp.handgun){
            long TheWeapon;// = *(long*)(Actor + Offsets::LastUpdateStatusKeyList + Offsets::EquipWeapon);
            ReadMemoryByTask(task, Actor + Offsets::LastUpdateStatusKeyList + Offsets::EquipWeapon, &TheWeapon, sizeof(TheWeapon));
            
            if(IsValidAddress(TheWeapon)){
                //WeaponID = *(int*)(TheWeapon + Offsets::RepWeaponID);//否则crash
                ReadMemoryByTask(task, Actor + Offsets::LastUpdateStatusKeyList + Offsets::EquipWeapon, &TheWeapon, sizeof(int));
            }
        }
        NSLog(@"[情情]:%s:alldata::HP:%f::IsAi:%d::Distance:%d::WeaponID:%d", __func__, Hp, IsAi, distance, WeaponID);
        alldata.HP = Hp;
        alldata.IsAi = IsAi;
        alldata.Distance = distance;
        alldata.WeaponID = WeaponID;
        NSData *tmp = [[NSData alloc]initWithBytes:&alldata length:sizeof(alldata)];
        [temparr addObject:tmp];
    } // for
    
    if(/*IsValidAddress(MySelf)*/NO){ // NO暂时不执行
        [self ModifyWeaponData];
        if(udp.aimpoint!=0){
        //isFire = *(bool*)(MySelf + Offsets::bIsWeaponFiring);
        ReadMemoryByTask(task, MySelf + Offsets::bIsWeaponFiring, &isFire, sizeof(isFire));
            
            if(isFire){
                if (bastaimactoraddr!=0&&_aimpos != Vector3::Zero()) {
                    [self SilentAimbot]; //视角追
                    long WeaponManagerComponent;// = *(long*)(MySelf + Offsets::WeaponManagerComponent);
                    ReadMemoryByTask(task, MySelf + Offsets::WeaponManagerComponent, &WeaponManagerComponent, sizeof(WeaponManagerComponent));
                    
                    if(IsValidAddress(WeaponManagerComponent)){
                        long CurrentWeaponReplicated;// = *(long*)(WeaponManagerComponent + Offsets::CurrentWeaponReplicated);
                        ReadMemoryByTask(task, WeaponManagerComponent + Offsets::CurrentWeaponReplicated, &CurrentWeaponReplicated, sizeof(CurrentWeaponReplicated));
                        
                        if(IsValidAddress(CurrentWeaponReplicated)){
                            //ShootWeaponEntityComp = *(long*)(CurrentWeaponReplicated + Offsets::ShootWeaponEntityComp);
                            ReadMemoryByTask(task, CurrentWeaponReplicated + Offsets::ShootWeaponEntityComp, &ShootWeaponEntityComp, sizeof(ShootWeaponEntityComp));
                            
                            if(IsValidAddress(ShootWeaponEntityComp)){
                                //TempRecoil = *(float*)(ShootWeaponEntityComp + Offsets::AccessoriesVRecoilFactor);
                                ReadMemoryByTask(task, ShootWeaponEntityComp + Offsets::AccessoriesVRecoilFactor, &TempRecoil, sizeof(TempRecoil));
                                if(TempRecoil!=0){
                                    AccessoriesVRecoilFactor = TempRecoil;
                                    //RecoilKickADS = *(float*)(ShootWeaponEntityComp + Offsets::RecoilKickADS);
                                    ReadMemoryByTask(task, ShootWeaponEntityComp + Offsets::RecoilKickADS, &RecoilKickADS, sizeof(RecoilKickADS));
                                }
                                [self funaimbot];//函数自瞄
                            }
                        }
                    }
                } else {
                    long WeaponManagerComponent;// = *(long*)(MySelf + Offsets::WeaponManagerComponent);
                    ReadMemoryByTask(task, MySelf + Offsets::WeaponManagerComponent, &WeaponManagerComponent, sizeof(WeaponManagerComponent));
                    
                    if(IsValidAddress(WeaponManagerComponent)){
                        long CurrentWeaponReplicated;// = *(long*)(WeaponManagerComponent + Offsets::CurrentWeaponReplicated);
                        ReadMemoryByTask(task, WeaponManagerComponent + Offsets::CurrentWeaponReplicated, &CurrentWeaponReplicated, sizeof(CurrentWeaponReplicated));
                        
                        if(IsValidAddress(CurrentWeaponReplicated)){
                            //ShootWeaponEntityComp = *(long*)(CurrentWeaponReplicated + Offsets::ShootWeaponEntityComp);
                            ReadMemoryByTask(task, CurrentWeaponReplicated + Offsets::ShootWeaponEntityComp, &ShootWeaponEntityComp, sizeof(ShootWeaponEntityComp));
                            
                            if(IsValidAddress(ShootWeaponEntityComp)){
                                //TempRecoil = *(float*)(ShootWeaponEntityComp + Offsets::AccessoriesVRecoilFactor);
                                ReadMemoryByTask(task, ShootWeaponEntityComp + Offsets::AccessoriesVRecoilFactor, &TempRecoil, sizeof(TempRecoil));
                                
                                if(TempRecoil==0) {
                                    *(float*)(ShootWeaponEntityComp + Offsets::AccessoriesVRecoilFactor) = AccessoriesVRecoilFactor;//write memory未修改巨魔跨进程
                                    *(float*)(ShootWeaponEntityComp + Offsets::RecoilKickADS) = RecoilKickADS;//write memory未修改巨魔跨进程
                                }
                            }
                        }
                    }
                }
            } else{
                
            }
        }
    }
    
    bastaimactoraddr = 0;
    _aimpos = Vector3();
    NSLog(@"[情情]:%s:temparr count:%lu=======", __func__, (unsigned long)[temparr count]);
    if([temparr count]==0)return;
    if (block) {
        NSLog(@"[情情]:%s:block:绘制=======", __func__);
        block(temparr);
    }
}
#pragma mark - 功能
- (void)ModifyWeaponData
{
    if(!udp.wuhou)return;
    long WeaponManagerComponent;// = *(long*)(MySelf + Offsets::WeaponManagerComponent);
    ReadMemoryByTask(task, MySelf + Offsets::WeaponManagerComponent, &WeaponManagerComponent, sizeof(WeaponManagerComponent));
    
    if(!IsValidAddress(WeaponManagerComponent))return;
    long CurrentWeaponReplicated;// = *(long*)(WeaponManagerComponent + Offsets::CurrentWeaponReplicated);
    ReadMemoryByTask(task, WeaponManagerComponent + Offsets::CurrentWeaponReplicated, &CurrentWeaponReplicated, sizeof(CurrentWeaponReplicated));
    
    if(!IsValidAddress(CurrentWeaponReplicated))return;
        ShootWeaponEntityComp;// = *(long*)(CurrentWeaponReplicated + Offsets::ShootWeaponEntityComp);
        ReadMemoryByTask(task, CurrentWeaponReplicated + Offsets::ShootWeaponEntityComp, &ShootWeaponEntityComp, sizeof(ShootWeaponEntityComp));
        
    if(IsValidAddress(ShootWeaponEntityComp)){
        *(float*)(ShootWeaponEntityComp + Offsets::AccessoriesVRecoilFactor) = 0.001;//write memory未修改巨魔跨进程
        *(float*)(ShootWeaponEntityComp + Offsets::RecoilKickADS) = 0.001;//write memory未修改巨魔跨进程
    }
    
}
-(void)funaimbot
{
    if(TempRecoil!=0){
        *(float*)(ShootWeaponEntityComp + Offsets::AccessoriesVRecoilFactor) = 0;//write memory未修改巨魔跨进程
        *(float*)(ShootWeaponEntityComp + Offsets::RecoilKickADS) = 0;//write memory未修改巨魔跨进程
    }
    float distance = Vector3::Distance(_aimpos, POV.Location), temp = 1.23f;
    if (distance < 5000.f)       temp = 1.8f;  else if (distance < 10000.f) temp = 1.72f;
    else if (distance < 15000.f) temp = 1.23f; else if (distance < 20000.f) temp = 1.24f;
    else if (distance < 25000.f) temp = 1.25f; else if (distance < 30000.f) temp = 1.26f;
    long ControlRotation = localPlayerController + 0x570;
    // 子弹飞行时间
    float BulletFireSpeed;// = *(float*)(ShootWeaponEntityComp + Offsets::BulletFireSpeed);
    ReadMemoryByTask(task, ShootWeaponEntityComp + Offsets::BulletFireSpeed, &BulletFireSpeed, sizeof(BulletFireSpeed));

    float BulletFlyTime = distance / BulletFireSpeed;
    float secFlyTime = BulletFlyTime * temp;
    Vector3 VelocitySafety;// = *(Vector3*)(bastaimactoraddr + Offsets::VelocitySafety);
    ReadMemoryByTask(task, bastaimactoraddr + Offsets::VelocitySafety, &VelocitySafety, sizeof(VelocitySafety));

    // 预判目标位置
    Vector3 delta = Vector3(VelocitySafety.X * secFlyTime, VelocitySafety.Y * secFlyTime, VelocitySafety.Z * secFlyTime);
    // 目标位置角度
    FRotator Rotation = CalcAngle(_aimpos - POV.Location + delta);
    // 自己位置角度
    FRotator mouseCoord;// = *(FRotator*)(ControlRotation);
    ReadMemoryByTask(task, ControlRotation, &mouseCoord, sizeof(mouseCoord));
    
    FRotator aimbotMouseMove;
        aimbotMouseMove.Yaw = change(getAngleDifference(Rotation.Yaw, mouseCoord.Yaw) * udp.zhimiaosudu);
        aimbotMouseMove.Pitch = change(getAngleDifference(Rotation.Pitch, mouseCoord.Pitch) * udp.zhimiaosudu);
    if(!isnan(aimbotMouseMove.Yaw) && !isnan(aimbotMouseMove.Pitch)) {
            AddControllerYawInput(reinterpret_cast<void *>(MySelf), aimbotMouseMove.Yaw);
            AddControllerPitchInput(reinterpret_cast<void *>(MySelf), aimbotMouseMove.Pitch);
            AddControllerRollInput(reinterpret_cast<void *>(MySelf), 0);
    }
}
-(void)memaimbot
{
                    
    if(TempRecoil!=0)
    {
        *(float*)(ShootWeaponEntityComp + Offsets::AccessoriesVRecoilFactor) = 0;//写内存
        *(float*)(ShootWeaponEntityComp + Offsets::RecoilKickADS) = 0;//写内存
    }
    float distance = Vector3::Distance(_aimpos, POV.Location), temp = 1.23f;
    if (distance < 5000.f)       temp = 1.8f;  else if (distance < 10000.f) temp = 1.72f;
    else if (distance < 15000.f) temp = 1.23f; else if (distance < 20000.f) temp = 1.24f;
    else if (distance < 25000.f) temp = 1.25f; else if (distance < 30000.f) temp = 1.26f;
    long ControlRotation = localPlayerController + 0x570;
    // 子弹飞行时间
    float BulletFireSpeed;// = *(float*)(ShootWeaponEntityComp + Offsets::BulletFireSpeed);
    ReadMemoryByTask(task, ShootWeaponEntityComp + Offsets::BulletFireSpeed, &BulletFireSpeed, sizeof(BulletFireSpeed));
    
    float BulletFlyTime = distance / BulletFireSpeed;
    float secFlyTime = BulletFlyTime * temp;
    Vector3 VelocitySafety;// = *(Vector3*)(bastaimactoraddr + Offsets::VelocitySafety);
    ReadMemoryByTask(task, bastaimactoraddr + Offsets::VelocitySafety, &VelocitySafety, sizeof(VelocitySafety));
    
    // 预判目标位置
    Vector3 delta = Vector3(VelocitySafety.X * secFlyTime, VelocitySafety.Y * secFlyTime, VelocitySafety.Z * secFlyTime);
    // 目标位置角度
    FRotator Rotation = CalcAngle(_aimpos - POV.Location + delta);
    // 自己位置角度
    FRotator mouseCoord;// = *(FRotator*)(ControlRotation);
    ReadMemoryByTask(task, ControlRotation, &mouseCoord, sizeof(mouseCoord));
    

    float RecoilKickADS;// = *(float*)(ShootWeaponEntityComp + Offsets::RecoilKickADS);
    ReadMemoryByTask(task, ShootWeaponEntityComp + Offsets::RecoilKickADS, &RecoilKickADS, sizeof(RecoilKickADS));
    
    float recoiltimes = 4.5 - (distance/10000);
    recoiltimes += (distance/10000) * udp.yaqiang;
    Rotation.Pitch -= recoiltimes*RecoilKickADS;

    FRotator aimbotMouseMove;
        aimbotMouseMove.Yaw = change(getAngleDifference(Rotation.Yaw, mouseCoord.Yaw) * 0.2);
        aimbotMouseMove.Pitch = change(getAngleDifference(Rotation.Pitch, mouseCoord.Pitch) * 0.2);
    // 控制相机旋转
    float Yaw = mouseCoord.Yaw + aimbotMouseMove.Yaw;
    float Pitch = mouseCoord.Pitch + aimbotMouseMove.Pitch;
    if(!isnan(Yaw) && !isnan(Pitch)) {
        *(float*)(ControlRotation) = Pitch;//write memory未修改巨魔跨进程
        *(float*)(ControlRotation + 4) = Yaw;//write memory未修改巨魔跨进程
    }
}
- (void)SilentAimbot//视角追
{
    if(!udp.bshijiao)return;
    Vector3 diffV3 = _aimpos - POV.Location;
    float pitch = atan2f(diffV3.Z, sqrt(diffV3.X * diffV3.X + diffV3.Y * diffV3.Y)) * 57.29577951308f;
    float yaw = atan2f(diffV3.Y, diffV3.X) * 57.29577951308f;
    *(float*)(playerCameraManager + 0x5c8) = pitch;//write memory未修改巨魔跨进程
    *(float*)(playerCameraManager + 0x5cc) = yaw;//write memory未修改巨魔跨进程
}
#pragma mark - 绘图
- (void)DrawLine:(Vector2)begin end:(Vector2)end visible:(int)visible
{
    UIBezierPath* Line = [UIBezierPath bezierPath];
    [Line moveToPoint:CGPointMake(begin.X, begin.Y)];
    [Line addLineToPoint:CGPointMake(end.X,end.Y)];
    if (visible)
        [_GreenLayer appendPath:Line];
    else
        [_YellowLayer appendPath:Line];
}
- (void)DrawWhiteLine:(Vector2)begin end:(Vector2)end
{
    UIBezierPath* Line = [UIBezierPath bezierPath];
    [Line moveToPoint:CGPointMake(begin.X, begin.Y)];
    [Line addLineToPoint:CGPointMake(end.X,end.Y)];
    [_WhiteLayer appendPath:Line];
}
- (void)DrawRedLine:(Vector2)begin end:(Vector2)end
{
    UIBezierPath* Line = [UIBezierPath bezierPath];
    [Line moveToPoint:CGPointMake(begin.X, begin.Y)];
    [Line addLineToPoint:CGPointMake(end.X,end.Y)];
    [_RedLayer appendPath:Line];
}
- (void)DrawCircle:(Vector2)center radius:(int)r color:(NSString*)color
{
    CGPoint centerPoint = CGPointMake(center.X, center.Y);
    UIBezierPath* cricle = [UIBezierPath bezierPathWithArcCenter:centerPoint radius:r startAngle:0 endAngle:M_PI * 2  clockwise:YES];
    if ([color  isEqual: @"red"])
        [_RedLayer appendPath:cricle];
    else
        [_WhiteLayer appendPath:cricle];
}
- (void)DrawHead:(Vector2)center color:(NSString*)color distence:(int)dis
{
    float r = 40.0f/dis;
    CGPoint centerPoint = CGPointMake(center.X, center.Y-r);
    UIBezierPath* cricle = [UIBezierPath bezierPathWithArcCenter:centerPoint radius:r startAngle:0 endAngle:M_PI * 2  clockwise:YES];
    if ([color  isEqual: @"red"])
        [_YellowLayer appendPath:cricle];
    else
        [_WhiteLayer appendPath:cricle];
}

@end

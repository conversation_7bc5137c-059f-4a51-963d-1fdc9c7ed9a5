
#ifndef Struct_h
#define Struct_h
#include <string>
struct Vector2 {
    float X;
    float Y;

    Vector2() {
        this->X = 0;
        this->Y = 0;
    }

    Vector2(float x, float y) {
        this->X = x;
        this->Y = y;
    }
    
    static float Distance(Vector2 a, Vector2 b) {
        Vector2 vector = Vector2(a.X - b.X, a.Y - b.Y);
        return sqrt((vector.X * vector.X) + (vector.Y * vector.Y));
    }

    Vector2 &operator+=(const Vector2 &v) {
        X += v.X;
        Y += v.Y;
        return *this;
    }

    Vector2 &operator-=(const Vector2 &v) {
        X -= v.X;
        Y -= v.Y;
        return *this;
    }
};

struct Vector3 {
    float X;
    float Y;
    float Z;

    Vector3() {
        this->X = 0;
        this->Y = 0;
        this->Z = 0;
    }

    Vector3(float x, float y, float z) {
        this->X = x;
        this->Y = y;
        this->Z = z;
    }

    Vector3 operator+(const Vector3 &v) const {
        return Vector3(X + v.X, Y + v.Y, Z + v.Z);
    }

    Vector3 operator-(const Vector3 &v) const {
        return Vector3(X - v.X, Y - v.Y, Z - v.Z);
    }

    bool operator==(const Vector3 &v) {
        return X == v.X && Y == v.Y && Z == v.Z;
    }

    bool operator!=(const Vector3 &v) {
        return !(X == v.X && Y == v.Y && Z == v.Z);
    }

    static Vector3 Zero() {
        return Vector3(0.0f, 0.0f, 0.0f);
    }

    static float Dot(Vector3 lhs, Vector3 rhs) {
        return (((lhs.X * rhs.X) + (lhs.Y * rhs.Y)) + (lhs.Z * rhs.Z));
    }

    static float Distance(Vector3 a, Vector3 b) {
        Vector3 vector = Vector3(a.X - b.X, a.Y - b.Y, a.Z - b.Z);
        return sqrt(((vector.X * vector.X) + (vector.Y * vector.Y)) + (vector.Z * vector.Z));
    }
};

struct FMatrix {
    float Matrix[4][4];

    float *operator[](int index) {
        return Matrix[index];
    }
};

struct Quat {
    float x;
    float y;
    float z;
    float w;
};

struct FTransform {
    Quat Rotation;
    Quat Translation;
    Vector3 Scale3D;
};

struct FRotator {
    float Pitch;
    float Yaw;
    float Roll;
};

struct MinimalViewInfo {
    Vector3 Location;
    Vector3 LocationLocalSpace;
    FRotator Rotation;
    float FOV;
};

struct BoneData
{
    Vector2 head;
    Vector2 chest;
    Vector2 leftShoulder;
    Vector2 rightShoulder;
    Vector2 leftElbow;
    Vector2 rightElbow;
    Vector2 leftHand;
    Vector2 rightHand;
    Vector2 pelvis;
    Vector2 leftThigh;
    Vector2 rightThigh;
    Vector2 leftKnee;
    Vector2 rightKnee;
    Vector2 leftFoot;
    Vector2 rightFoot;
};

struct BoneVisibleData
{
    bool head;
    bool chest;
    bool leftShoulder;
    bool rightShoulder;
    bool leftElbow;
    bool rightElbow;
    bool leftHand;
    bool rightHand;
    bool pelvis;
    bool leftThigh;
    bool rightThigh;
    bool leftKnee;
    bool rightKnee;
    bool leftFoot;
    bool rightFoot;
};

typedef struct ActorInfo
{
    int type;
    float X;
    float Y;
    float HP;
    bool IsAi;
    int Distance;
    int Team;
    std::string name;
    //BoneData Bone;
    //BoneVisibleData BoneVisible;
    int WeaponID;
}ActorInfo;

#endif /* Struct_h */

--- !tapi-tbd
tbd-version:     4
targets:         [ i386-ios-simulator, x86_64-ios-simulator, arm64-ios-simulator ]
uuids:
  - target:          i386-ios-simulator
    value:           C76B0FD8-49B1-3DE1-8CAA-29E9B6EC6DC6
  - target:          x86_64-ios-simulator
    value:           B540F98B-8744-3839-B806-FBB7FBB97553
  - target:          arm64-ios-simulator
    value:           38B25309-A406-33C0-8AB1-1C749E42D73D
install-name:    '/System/Library/Frameworks/MetalKit.framework/MetalKit'
current-version: 154
exports:
  - targets:         [ i386-ios-simulator ]
    symbols:         [ _MTKTextureLoaderOptionCubeFromVerticalTexture, _MetalKitVersionNumber, 
                       _MetalKitVersionString, _PVR3SizedFormat, __MTKModelErrorWithCode, 
                       __MTKModelErrorWithCodeAndErrorString, __MTKModelErrorWithCodeAndUserInfo, 
                       __mtkLinkedOSVersion, __mtkLinkedOnOrAfter, __newMTKTextureErrorWithCode, 
                       __newMTKTextureErrorWithCodeAndErrorString, __newMTKTextureErrorWithCodeAndUserInfo, 
                       _gCGColorSpaceModelString, _gCGImageAlphaString, _gCGImageBitmapByteOrder, 
                       _gCGImageComponentTypeString, _gKTXFileIdentifier, _printIndices16, 
                       _printIndices32, _reflectImage ]
    objc-classes:    [ MTKMeshBufferHolder, MTKMeshBufferZone, MTKTextureLoaderData, 
                       MTKTextureLoaderImageIO, MTKTextureLoaderKTX, MTKTextureLoaderMDL, 
                       MTKTextureLoaderPVR, MTKTextureLoaderPVR3, MTKTextureUploader ]
  - targets:         [ i386-ios-simulator, x86_64-ios-simulator, arm64-ios-simulator ]
    symbols:         [ _MTKMetalVertexDescriptorFromModelIO, _MTKMetalVertexDescriptorFromModelIOWithError, 
                       _MTKMetalVertexFormatFromModelIO, _MTKModelErrorDomain, _MTKModelErrorKey, 
                       _MTKModelIOVertexDescriptorFromMetal, _MTKModelIOVertexDescriptorFromMetalWithError, 
                       _MTKModelIOVertexFormatFromMetal, _MTKTextureLoaderCubeLayoutVertical, 
                       _MTKTextureLoaderErrorDomain, _MTKTextureLoaderErrorKey, _MTKTextureLoaderOptionAllocateMipmaps, 
                       _MTKTextureLoaderOptionCubeLayout, _MTKTextureLoaderOptionGenerateMipmaps, 
                       _MTKTextureLoaderOptionOrigin, _MTKTextureLoaderOptionPackedRowStride, 
                       _MTKTextureLoaderOptionSRGB, _MTKTextureLoaderOptionTextureCPUCacheMode, 
                       _MTKTextureLoaderOptionTextureStorageMode, _MTKTextureLoaderOptionTextureUsage, 
                       _MTKTextureLoaderOriginBottomLeft, _MTKTextureLoaderOriginFlippedVertically, 
                       _MTKTextureLoaderOriginTopLeft ]
    objc-classes:    [ MTKMesh, MTKMeshBuffer, MTKMeshBufferAllocator, MTKSubmesh, 
                       MTKTextureIOBuffer, MTKTextureIOBufferAllocator, MTKTextureIOBufferMap, 
                       MTKTextureLoader, MTKView ]
...

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/imconfig.h</key>
		<data>
		QBfJCjI8dY4Lrfw/dRQyq7lZ6Zo=
		</data>
		<key>Headers/imgui.h</key>
		<data>
		IyCyQ2+OpsLo4OZgMWR8Y9Gu7Dg=
		</data>
		<key>Headers/imgui_impl_metal.h</key>
		<data>
		Hmlr2joObrD9UikVHNmH00iAGe8=
		</data>
		<key>Headers/imgui_internal.h</key>
		<data>
		gZT0999beSu10hH9uFtV4tmS9zk=
		</data>
		<key>Headers/imstb_rectpack.h</key>
		<data>
		6S3TVqOiOHTI3kC01GF4JbW139k=
		</data>
		<key>Headers/imstb_textedit.h</key>
		<data>
		NuwI2bt5ItoAslbc+tNoxo/5IIA=
		</data>
		<key>Headers/imstb_truetype.h</key>
		<data>
		bVWBh07kAf0uIYlOVA4io5GjcE0=
		</data>
		<key>Info.plist</key>
		<data>
		8wV/psl4CIAtL4oPWK4tCsiPGXI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/imconfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			QBfJCjI8dY4Lrfw/dRQyq7lZ6Zo=
			</data>
			<key>hash2</key>
			<data>
			Pxdh8CAAFHkgYccdhy5vvmz/buCK1aaxmB7Yjz5g/08=
			</data>
		</dict>
		<key>Headers/imgui.h</key>
		<dict>
			<key>hash</key>
			<data>
			IyCyQ2+OpsLo4OZgMWR8Y9Gu7Dg=
			</data>
			<key>hash2</key>
			<data>
			PvOTzEhLlTb1KPNpIH8xUOW0Q1EE83VUqc746TwtglQ=
			</data>
		</dict>
		<key>Headers/imgui_impl_metal.h</key>
		<dict>
			<key>hash</key>
			<data>
			Hmlr2joObrD9UikVHNmH00iAGe8=
			</data>
			<key>hash2</key>
			<data>
			uYTeGK4qAJ1mP3VzewzY68aam5yAwL+6oATqB3kH5n4=
			</data>
		</dict>
		<key>Headers/imgui_internal.h</key>
		<dict>
			<key>hash</key>
			<data>
			gZT0999beSu10hH9uFtV4tmS9zk=
			</data>
			<key>hash2</key>
			<data>
			FK6U/ZX9tcPeHVNDwm4KCxtaUXiQxUQRlDUqDoLHARc=
			</data>
		</dict>
		<key>Headers/imstb_rectpack.h</key>
		<dict>
			<key>hash</key>
			<data>
			6S3TVqOiOHTI3kC01GF4JbW139k=
			</data>
			<key>hash2</key>
			<data>
			Yet5UORdSDH6rGYppyesRk3c/PGZ6yentH3gK4mKtu8=
			</data>
		</dict>
		<key>Headers/imstb_textedit.h</key>
		<dict>
			<key>hash</key>
			<data>
			NuwI2bt5ItoAslbc+tNoxo/5IIA=
			</data>
			<key>hash2</key>
			<data>
			/YQIWEhC0Tyko/WwAqd8gEkWJtKJqWRjulLBGYGBYCQ=
			</data>
		</dict>
		<key>Headers/imstb_truetype.h</key>
		<dict>
			<key>hash</key>
			<data>
			bVWBh07kAf0uIYlOVA4io5GjcE0=
			</data>
			<key>hash2</key>
			<data>
			gNDFdEe0A2F/3dzL/7jaUJ+j99fbWzqs2bczBh1aP2c=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleDisplayName</key>
	<string>Trollspeed</string>
	<key>CFBundleExecutable</key>
	<string>TrollSpeed-Sandbox</string>
	<key>CFBundleIconFile</key>
	<string>icon.png</string>
	<key>CFBundleIconName</key>
	<string>AppIcon</string>
	<key>CFBundleIdentifier</key>
	<string>com.LLLL.plugin-sandbox</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>TrollSpeed</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.11.11</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>ch.xxtou.hudapp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>trollspeed</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>c4b10404</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.developer-tools</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2023-2024 Lessica &amp; Lakr Aream. All Rights Reserved.</string>
	<key>NSPrincipalClass</key>
	<string>HUDMainApplication</string>
	<key>UIApplicationShortcutItems</key>
	<array>
		<dict>
			<key>UIApplicationShortcutItemIconSymbolName</key>
			<string>wand.and.stars</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>Toggle HUD</string>
			<key>UIApplicationShortcutItemType</key>
			<string>ch.xxtou.shortcut.toggle-hud</string>
		</dict>
	</array>
	<key>UIApplicationShowsViewsWhileLocked</key>
	<false/>
	<key>UIApplicationSystemWindowsSecureKey</key>
	<true/>
	<key>UIBackgroundStyle</key>
	<string>UIBackgroundStyleDarkBlur</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
